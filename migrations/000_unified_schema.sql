-- Migration 000: Unified Database Schema
-- This is a complete, clean-slate database schema that consolidates 15+ migrations
-- and fixes all accumulated technical debt and inconsistencies.
-- 
-- Design Principles:
-- 1. All primary keys are TEXT (for UUIDs)
-- 2. All tables have complete audit fields (created_at, updated_at, created_by, updated_by, deleted_at)
-- 3. Foreign keys use ON DELETE SET NULL by default (CASCADE only for junction tables)
-- 4. Consistent data types: TEXT for strings/timestamps, INTEGER for numbers/booleans
-- 5. All tables support soft delete with proper indexes
-- 6. CHECK constraints enforce business rules
-- 7. Comprehensive indexing strategy for performance

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- MIGRATION TRACKING
-- ============================================================================

-- Migration tracking table
CREATE TABLE IF NOT EXISTS migrations (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  executed_at TEXT NOT NULL DEFAULT (datetime('now')),
  executed_by TEXT,
  checksum TEXT, -- For migration integrity verification
  rollback_sql TEXT, -- Store rollback commands
  execution_time_ms INTEGER -- Track migration performance
);

CREATE INDEX idx_migrations_executed_at ON migrations(executed_at);
CREATE INDEX idx_migrations_name ON migrations(name);

-- ============================================================================
-- CORE ENTITY TABLES
-- ============================================================================

-- Company table - Core business entity
CREATE TABLE IF NOT EXISTS company (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  domain TEXT,
  industry TEXT,
  size TEXT,
  description TEXT,
  website TEXT,
  phone TEXT,
  email TEXT,
  address TEXT,
  abn TEXT, -- Australian Business Number
  
  -- Address fields
  address_line1 TEXT,
  address_line2 TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  
  -- External system IDs
  hubspot_id TEXT UNIQUE,
  harvest_id INTEGER UNIQUE,
  xero_id TEXT UNIQUE,
  
  -- Metadata
  company_type TEXT CHECK(company_type IN ('client', 'prospect', 'partner', 'vendor', 'other')),
  status TEXT DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'archived')),
  source TEXT CHECK(source IN ('manual', 'Manual', 'hubspot', 'HubSpot', 'harvest', 'Harvest', 'xero', 'Xero', 'import', 'Import')),
  
  -- Radar/CRM fields
  radar_state TEXT,
  priority TEXT,
  current_spend REAL,
  potential_spend REAL,
  last_interaction_date TEXT,
  notes TEXT,
  
  -- Enrichment tracking
  enrichment_status TEXT DEFAULT '{}', -- JSON
  last_enriched_at TEXT,
  enrichment_source TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  CHECK(email IS NULL OR email LIKE '%@%.%'),
  CHECK(abn IS NULL OR length(abn) = 11)
);

-- Company indexes
CREATE INDEX idx_company_name ON company(name);
CREATE INDEX idx_company_domain ON company(domain);
CREATE INDEX idx_company_hubspot_id ON company(hubspot_id);
CREATE INDEX idx_company_harvest_id ON company(harvest_id);
CREATE INDEX idx_company_xero_id ON company(xero_id);
CREATE INDEX idx_company_deleted_at ON company(deleted_at);
CREATE INDEX idx_company_status ON company(status);
CREATE INDEX idx_company_type ON company(company_type);
CREATE INDEX idx_company_source ON company(source);

-- Contact table - Individual people
CREATE TABLE IF NOT EXISTS contact (
  id TEXT PRIMARY KEY,
  email TEXT, -- Not unique per migration 012
  first_name TEXT,
  last_name TEXT,
  full_name TEXT GENERATED ALWAYS AS (TRIM(COALESCE(first_name, '') || ' ' || COALESCE(last_name, ''))) STORED,
  phone TEXT,
  mobile TEXT,
  
  -- Professional info
  job_title TEXT,
  department TEXT,
  seniority_level TEXT,
  notes TEXT,
  
  -- External system IDs
  hubspot_id TEXT UNIQUE,
  harvest_user_id INTEGER UNIQUE,
  
  -- Metadata
  status TEXT DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'archived')),
  source TEXT CHECK(source IN ('manual', 'Manual', 'hubspot', 'HubSpot', 'harvest', 'Harvest', 'xero', 'Xero', 'import', 'Import')),
  contact_type TEXT CHECK(contact_type IN ('primary', 'secondary', 'technical', 'billing', 'other')),
  
  -- Enrichment tracking
  enrichment_status TEXT DEFAULT '{}', -- JSON
  last_enriched_at TEXT,
  
  -- Communication preferences
  preferred_contact_method TEXT CHECK(preferred_contact_method IN ('email', 'phone', 'mobile', NULL)),
  timezone TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  CHECK(email IS NULL OR email LIKE '%@%.%')
);

-- Contact indexes
CREATE INDEX idx_contact_email ON contact(email);
CREATE INDEX idx_contact_full_name ON contact(full_name);
CREATE INDEX idx_contact_hubspot_id ON contact(hubspot_id);
CREATE INDEX idx_contact_harvest_user_id ON contact(harvest_user_id);
CREATE INDEX idx_contact_deleted_at ON contact(deleted_at);
CREATE INDEX idx_contact_status ON contact(status);

-- Deal table - Sales opportunities
CREATE TABLE IF NOT EXISTS deal (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  company_id TEXT,
  
  -- Deal details
  stage TEXT NOT NULL,
  status TEXT DEFAULT 'open' CHECK(status IN ('open', 'won', 'lost', 'abandoned')),
  value REAL DEFAULT 0,
  currency TEXT DEFAULT 'AUD',
  probability REAL DEFAULT 0 CHECK(probability >= 0 AND probability <= 100),
  
  -- Important dates
  expected_close_date TEXT,
  actual_close_date TEXT,
  start_date TEXT,
  end_date TEXT,
  
  -- Financial details
  invoice_frequency TEXT CHECK(invoice_frequency IN ('once', 'weekly', 'fortnightly', 'monthly', 'quarterly', 'annually', NULL)),
  payment_terms INTEGER DEFAULT 30,
  
  -- External references
  hubspot_id TEXT UNIQUE,
  harvest_id INTEGER, -- For backward compatibility with existing code
  harvest_project_id INTEGER,
  estimate_id TEXT,
  estimate_type TEXT CHECK(estimate_type IN ('internal', 'harvest', NULL)),
  
  -- HubSpot preserved values
  hubspot_name TEXT,
  hubspot_value REAL,
  
  -- Additional info
  description TEXT,
  source TEXT CHECK(source IN ('manual', 'Manual', 'hubspot', 'HubSpot', 'harvest', 'Harvest', 'xero', 'Xero', 'referral', 'website', 'other')),
  priority TEXT CHECK(priority IN ('low', 'medium', 'high', 'critical')),
  owner TEXT,
  close_reason TEXT,
  competitors TEXT, -- JSON array
  next_steps TEXT,
  custom_fields TEXT DEFAULT '{}', -- JSON
  
  -- Projection settings
  include_in_projections INTEGER DEFAULT 1 CHECK(include_in_projections IN (0, 1)),
  field_ownership TEXT DEFAULT '{}', -- JSON tracking which fields are controlled by estimates
  
  -- Activity tracking
  last_activity_date TEXT,
  days_since_last_activity INTEGER GENERATED ALWAYS AS (
    CASE 
      WHEN last_activity_date IS NULL THEN NULL
      ELSE CAST(julianday('now') - julianday(last_activity_date) AS INTEGER)
    END
  ) STORED,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(start_date IS NULL OR end_date IS NULL OR start_date <= end_date),
  CHECK(expected_close_date IS NULL OR actual_close_date IS NULL OR expected_close_date <= actual_close_date)
);

-- Deal indexes
CREATE INDEX idx_deal_company_id ON deal(company_id);
CREATE INDEX idx_deal_stage ON deal(stage);
CREATE INDEX idx_deal_status ON deal(status);
CREATE INDEX idx_deal_hubspot_id ON deal(hubspot_id);
CREATE INDEX idx_deal_harvest_id ON deal(harvest_id);
CREATE INDEX idx_deal_harvest_project_id ON deal(harvest_project_id);
CREATE INDEX idx_deal_estimate_id ON deal(estimate_id);
CREATE INDEX idx_deal_expected_close_date ON deal(expected_close_date);
CREATE INDEX idx_deal_deleted_at ON deal(deleted_at);
CREATE INDEX idx_deal_include_in_projections ON deal(include_in_projections);
CREATE INDEX idx_deal_owner ON deal(owner);

-- Estimate table - Project estimates and proposals
CREATE TABLE IF NOT EXISTS estimate (
  id TEXT PRIMARY KEY,
  company_id TEXT,
  
  -- Basic info
  client_name TEXT NOT NULL,
  project_name TEXT,
  project_code TEXT,
  estimate_number TEXT,
  
  -- Estimate metadata
  date_sent TEXT,
  valid_until TEXT,
  
  -- Project timeline
  start_date TEXT,
  end_date TEXT,
  
  -- Financial details
  discount_type TEXT DEFAULT 'none' CHECK(discount_type IN ('none', 'percentage', 'fixed')),
  discount_value REAL DEFAULT 0,
  invoice_frequency TEXT CHECK(invoice_frequency IN ('once', 'weekly', 'fortnightly', 'monthly', 'quarterly', 'annually', NULL)),
  payment_terms INTEGER DEFAULT 30,
  payment_schedule TEXT, -- JSON
  
  -- Calculated totals
  total_consultancy REAL DEFAULT 0,
  total_expenses REAL DEFAULT 0,
  total REAL DEFAULT 0,
  tax REAL DEFAULT 0,
  grand_total REAL DEFAULT 0,
  
  -- Status tracking
  status TEXT DEFAULT 'draft' CHECK(status IN ('draft', 'sent', 'approved', 'rejected', 'expired', 'withdrawn')),
  version INTEGER DEFAULT 1,
  notes TEXT,
  
  -- External references
  harvest_estimate_id TEXT UNIQUE,
  
  -- Staff allocations (JSON)
  staff_allocations TEXT DEFAULT '[]',
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(valid_until IS NULL OR date_sent IS NULL OR date_sent <= valid_until),
  CHECK(start_date IS NULL OR end_date IS NULL OR start_date <= end_date),
  CHECK(discount_value >= 0),
  CHECK(total >= 0),
  CHECK(grand_total >= 0)
);

-- Estimate indexes
CREATE INDEX idx_estimate_company_id ON estimate(company_id);
CREATE INDEX idx_estimate_harvest_estimate_id ON estimate(harvest_estimate_id);
CREATE INDEX idx_estimate_status ON estimate(status);
CREATE INDEX idx_estimate_date_sent ON estimate(date_sent);
CREATE INDEX idx_estimate_deleted_at ON estimate(deleted_at);

-- Project table - First-class project entity
CREATE TABLE IF NOT EXISTS project (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  company_id TEXT,
  deal_id TEXT,
  
  -- Project details
  code TEXT,
  description TEXT,
  project_type TEXT CHECK(project_type IN ('fixed', 'hourly', 'retainer', 'other')),
  status TEXT DEFAULT 'planning' CHECK(status IN ('planning', 'active', 'paused', 'completed', 'cancelled')),
  
  -- Timeline
  start_date TEXT,
  end_date TEXT,
  
  -- Budget
  budget REAL,
  budget_type TEXT CHECK(budget_type IN ('total', 'monthly', 'quarterly', 'annual')),
  hourly_rate REAL,
  
  -- External references
  harvest_project_id INTEGER UNIQUE,
  
  -- Team and dependencies tracked in separate tables
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(start_date IS NULL OR end_date IS NULL OR start_date <= end_date)
);

-- Project indexes
CREATE INDEX idx_project_company_id ON project(company_id);
CREATE INDEX idx_project_deal_id ON project(deal_id);
CREATE INDEX idx_project_harvest_project_id ON project(harvest_project_id);
CREATE INDEX idx_project_status ON project(status);
CREATE INDEX idx_project_deleted_at ON project(deleted_at);

-- Expense table - Financial expenses tracking
CREATE TABLE IF NOT EXISTS expense (
  id TEXT PRIMARY KEY,
  
  -- Basic info
  name TEXT,
  description TEXT,
  amount REAL NOT NULL,
  date TEXT NOT NULL,
  
  -- Categorization
  type TEXT,
  category TEXT,
  subcategory TEXT,
  expense_type TEXT CHECK(expense_type IN ('fixed', 'variable', 'one-time', 'recurring')),
  
  -- Recurring info
  frequency TEXT,
  repeat_count INTEGER,
  
  -- Payment info
  payment_method TEXT,
  vendor TEXT,
  invoice_number TEXT,
  
  -- External references
  xero_expense_id TEXT UNIQUE,
  harvest_expense_id INTEGER UNIQUE,
  
  -- Source and editability
  source TEXT DEFAULT 'manual',
  editable INTEGER DEFAULT 1 CHECK(editable IN (0, 1)),
  
  -- Metadata
  metadata TEXT DEFAULT '{}', -- JSON object for flexible data
  tags TEXT DEFAULT '[]', -- JSON array
  notes TEXT,
  attachments TEXT DEFAULT '[]', -- JSON array of file references
  
  -- Approval workflow
  status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'rejected', 'paid')),
  approved_by TEXT,
  approved_at TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  CHECK(amount > 0)
);

-- Expense indexes
CREATE INDEX idx_expense_date ON expense(date);
CREATE INDEX idx_expense_category ON expense(category);
CREATE INDEX idx_expense_vendor ON expense(vendor);
CREATE INDEX idx_expense_status ON expense(status);
CREATE INDEX idx_expense_deleted_at ON expense(deleted_at);

-- Note table - Generic notes with conversation threading
CREATE TABLE IF NOT EXISTS note (
  id TEXT PRIMARY KEY,
  
  -- Polymorphic association
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact', 'deal', 'project', 'estimate')),
  entity_id TEXT NOT NULL,
  
  -- Note content
  content TEXT NOT NULL,
  note_type TEXT DEFAULT 'general' CHECK(note_type IN ('general', 'meeting', 'call', 'email', 'task', 'reminder')),
  
  -- Conversation threading
  parent_note_id TEXT,
  thread_id TEXT, -- Root note ID for the thread
  
  -- Metadata
  is_pinned INTEGER DEFAULT 0 CHECK(is_pinned IN (0, 1)),
  is_private INTEGER DEFAULT 0 CHECK(is_private IN (0, 1)),
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (parent_note_id) REFERENCES note(id) ON DELETE SET NULL
);

-- Note indexes
CREATE INDEX idx_note_entity ON note(entity_type, entity_id);
CREATE INDEX idx_note_parent_note_id ON note(parent_note_id);
CREATE INDEX idx_note_thread_id ON note(thread_id);
CREATE INDEX idx_note_type ON note(note_type);
CREATE INDEX idx_note_deleted_at ON note(deleted_at);

-- Tender table - Tender/RFP tracking
CREATE TABLE IF NOT EXISTS tender (
  id TEXT PRIMARY KEY,
  company_id TEXT,
  deal_id TEXT,
  
  -- Tender details
  title TEXT NOT NULL,
  request_no TEXT UNIQUE,
  tender_type TEXT CHECK(tender_type IN ('RFP', 'RFQ', 'EOI', 'RFI', 'other')),
  description TEXT,
  
  -- Important dates
  issue_date TEXT,
  due_date TEXT,
  submission_date TEXT,
  
  -- Status tracking
  status TEXT DEFAULT 'draft' CHECK(status IN ('draft', 'preparing', 'submitted', 'won', 'lost', 'withdrawn')),
  outcome TEXT,
  feedback TEXT,
  
  -- Financial
  estimated_value REAL,
  submitted_value REAL,
  
  -- Team and effort
  lead_person TEXT,
  team_members TEXT DEFAULT '[]', -- JSON array
  estimated_effort_hours REAL,
  actual_effort_hours REAL,
  
  -- Documents and compliance
  requirements TEXT DEFAULT '[]', -- JSON array
  compliance_checklist TEXT DEFAULT '[]', -- JSON array
  documents TEXT DEFAULT '[]', -- JSON array of document references
  
  -- Scoring and evaluation
  technical_score REAL,
  commercial_score REAL,
  total_score REAL,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(issue_date IS NULL OR due_date IS NULL OR issue_date <= due_date),
  CHECK(technical_score IS NULL OR (technical_score >= 0 AND technical_score <= 100)),
  CHECK(commercial_score IS NULL OR (commercial_score >= 0 AND commercial_score <= 100))
);

-- Tender indexes
CREATE INDEX idx_tender_company_id ON tender(company_id);
CREATE INDEX idx_tender_deal_id ON tender(deal_id);
CREATE INDEX idx_tender_request_no ON tender(request_no);
CREATE INDEX idx_tender_status ON tender(status);
CREATE INDEX idx_tender_due_date ON tender(due_date);
CREATE INDEX idx_tender_deleted_at ON tender(deleted_at);

-- ============================================================================
-- JUNCTION/RELATIONSHIP TABLES
-- ============================================================================

-- Contact-Company relationship
CREATE TABLE IF NOT EXISTS contact_company (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  
  -- Relationship details
  role TEXT,
  is_primary INTEGER DEFAULT 0 CHECK(is_primary IN (0, 1)),
  start_date TEXT,
  end_date TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - CASCADE appropriate for junction table
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(contact_id, company_id),
  CHECK(start_date IS NULL OR end_date IS NULL OR start_date <= end_date)
);

-- Contact-Company indexes
CREATE INDEX idx_contact_company_contact_id ON contact_company(contact_id);
CREATE INDEX idx_contact_company_company_id ON contact_company(company_id);
CREATE INDEX idx_contact_company_role ON contact_company(role);
CREATE INDEX idx_contact_company_is_primary ON contact_company(is_primary);
CREATE INDEX idx_contact_company_deleted_at ON contact_company(deleted_at);

-- Contact role in deals
CREATE TABLE IF NOT EXISTS contact_role (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  deal_id TEXT NOT NULL,
  
  -- Role details
  role TEXT NOT NULL,
  is_primary INTEGER DEFAULT 0 CHECK(is_primary IN (0, 1)),
  influence_level TEXT CHECK(influence_level IN ('low', 'medium', 'high', 'decision_maker')),
  notes TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - Mixed strategy: preserve contact, remove if deal deleted
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE RESTRICT,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(contact_id, deal_id)
);

-- Contact role indexes
CREATE INDEX idx_contact_role_contact_id ON contact_role(contact_id);
CREATE INDEX idx_contact_role_deal_id ON contact_role(deal_id);
CREATE INDEX idx_contact_role_role ON contact_role(role);
CREATE INDEX idx_contact_role_is_primary ON contact_role(is_primary);
CREATE INDEX idx_contact_role_deleted_at ON contact_role(deleted_at);

-- Deal-Estimate linking (one-to-one as of migration 003)
CREATE TABLE IF NOT EXISTS deal_estimate (
  id TEXT PRIMARY KEY,
  deal_id TEXT NOT NULL UNIQUE, -- One-to-one constraint
  estimate_id TEXT NOT NULL UNIQUE, -- One-to-one constraint
  
  -- Link metadata
  relationship_type TEXT DEFAULT 'primary' CHECK(relationship_type IN ('primary', 'alternative', 'superseded')),
  estimate_type TEXT DEFAULT 'internal' CHECK(estimate_type IN ('internal', 'harvest')),
  harvest_estimate_id TEXT, -- For backward compatibility
  linked_at TEXT NOT NULL DEFAULT (datetime('now')),
  linked_by TEXT,
  notes TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - CASCADE appropriate for junction table
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
  FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
);

-- Deal-Estimate indexes
CREATE INDEX idx_deal_estimate_deal_id ON deal_estimate(deal_id);
CREATE INDEX idx_deal_estimate_estimate_id ON deal_estimate(estimate_id);
CREATE INDEX idx_deal_estimate_deleted_at ON deal_estimate(deleted_at);

-- Contact-to-Contact relationships
CREATE TABLE IF NOT EXISTS contact_relationships (
  id TEXT PRIMARY KEY,
  source_contact_id TEXT NOT NULL,
  target_contact_id TEXT NOT NULL,
  
  -- Relationship details
  relationship_type TEXT NOT NULL CHECK(relationship_type IN ('reports_to', 'colleague', 'partner', 'referral', 'other')),
  strength TEXT CHECK(strength IN ('weak', 'moderate', 'strong')),
  context TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - CASCADE appropriate for relationship table
  FOREIGN KEY (source_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
  FOREIGN KEY (target_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(source_contact_id, target_contact_id, relationship_type),
  CHECK(source_contact_id != target_contact_id)
);

-- Contact relationships indexes
CREATE INDEX idx_contact_relationships_source ON contact_relationships(source_contact_id);
CREATE INDEX idx_contact_relationships_target ON contact_relationships(target_contact_id);
CREATE INDEX idx_contact_relationships_type ON contact_relationships(relationship_type);
CREATE INDEX idx_contact_relationships_deleted_at ON contact_relationships(deleted_at);

-- Company-to-Company relationships (parent-child)
CREATE TABLE IF NOT EXISTS company_relationship (
  id TEXT PRIMARY KEY,
  parent_company_id TEXT NOT NULL,
  child_company_id TEXT NOT NULL,
  
  -- Relationship details
  relationship_type TEXT DEFAULT 'subsidiary' CHECK(relationship_type IN ('subsidiary', 'division', 'partner', 'affiliate')),
  ownership_percentage REAL CHECK(ownership_percentage IS NULL OR (ownership_percentage >= 0 AND ownership_percentage <= 100)),
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - CASCADE appropriate for relationship table
  FOREIGN KEY (parent_company_id) REFERENCES company(id) ON DELETE CASCADE,
  FOREIGN KEY (child_company_id) REFERENCES company(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(parent_company_id, child_company_id),
  CHECK(parent_company_id != child_company_id)
);

-- Company relationship indexes
CREATE INDEX idx_company_relationship_parent ON company_relationship(parent_company_id);
CREATE INDEX idx_company_relationship_child ON company_relationship(child_company_id);
CREATE INDEX idx_company_relationship_deleted_at ON company_relationship(deleted_at);

-- Project team members
CREATE TABLE IF NOT EXISTS project_contact (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  contact_id TEXT NOT NULL,
  
  -- Assignment details
  role TEXT NOT NULL,
  allocation_percentage REAL DEFAULT 100 CHECK(allocation_percentage > 0 AND allocation_percentage <= 100),
  start_date TEXT,
  end_date TEXT,
  
  -- Billing
  billable INTEGER DEFAULT 1 CHECK(billable IN (0, 1)),
  hourly_rate REAL,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - SET NULL to preserve history
  FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE SET NULL,
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE SET NULL,
  
  -- Constraints
  UNIQUE(project_id, contact_id),
  CHECK(start_date IS NULL OR end_date IS NULL OR start_date <= end_date)
);

-- Project contact indexes
CREATE INDEX idx_project_contact_project_id ON project_contact(project_id);
CREATE INDEX idx_project_contact_contact_id ON project_contact(contact_id);
CREATE INDEX idx_project_contact_role ON project_contact(role);
CREATE INDEX idx_project_contact_deleted_at ON project_contact(deleted_at);

-- Project dependencies
CREATE TABLE IF NOT EXISTS project_dependency (
  id TEXT PRIMARY KEY,
  predecessor_project_id TEXT NOT NULL,
  successor_project_id TEXT NOT NULL,
  
  -- Dependency details
  dependency_type TEXT NOT NULL CHECK(dependency_type IN ('blocks', 'requires', 'related_to')),
  lag_days INTEGER DEFAULT 0,
  notes TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - CASCADE appropriate for dependency tracking
  FOREIGN KEY (predecessor_project_id) REFERENCES project(id) ON DELETE CASCADE,
  FOREIGN KEY (successor_project_id) REFERENCES project(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(predecessor_project_id, successor_project_id),
  CHECK(predecessor_project_id != successor_project_id)
);

-- Project dependency indexes
CREATE INDEX idx_project_dependency_predecessor ON project_dependency(predecessor_project_id);
CREATE INDEX idx_project_dependency_successor ON project_dependency(successor_project_id);
CREATE INDEX idx_project_dependency_type ON project_dependency(dependency_type);
CREATE INDEX idx_project_dependency_deleted_at ON project_dependency(deleted_at);

-- ============================================================================
-- ALLOCATION TABLES
-- ============================================================================

-- Estimate allocations (staff assignments)
CREATE TABLE IF NOT EXISTS estimate_allocation (
  id TEXT PRIMARY KEY,
  estimate_id TEXT NOT NULL,
  
  -- Staff details
  harvest_user_id INTEGER,
  first_name TEXT NOT NULL,
  last_name TEXT,
  project_role TEXT,
  level TEXT,
  
  -- Rates
  target_rate_daily REAL NOT NULL,
  cost_rate_daily REAL NOT NULL,
  proposed_rate_daily REAL NOT NULL,
  
  -- Allocation
  total_days REAL,
  total_hours REAL GENERATED ALWAYS AS (total_days * 8) STORED,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - RESTRICT to prevent accidental deletion
  FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE RESTRICT
);

-- Estimate allocation indexes
CREATE INDEX idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id);
CREATE INDEX idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id);
CREATE INDEX idx_estimate_allocation_deleted_at ON estimate_allocation(deleted_at);

-- Time allocations by week
CREATE TABLE IF NOT EXISTS estimate_time_allocation (
  id TEXT PRIMARY KEY,
  allocation_id TEXT NOT NULL,
  
  -- Time period
  week_identifier TEXT NOT NULL, -- Format: 'YYYY-WW' (ISO week number)
  
  -- Allocation
  days REAL NOT NULL CHECK(days >= 0 AND days <= 5),
  hours REAL GENERATED ALWAYS AS (days * 8) STORED,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - RESTRICT to prevent accidental deletion
  FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE RESTRICT,
  
  -- Constraints
  UNIQUE(allocation_id, week_identifier),
  CHECK(week_identifier GLOB '[0-9][0-9][0-9][0-9]-[0-9][0-9]')
);

-- Time allocation indexes
CREATE INDEX idx_time_allocation_allocation_id ON estimate_time_allocation(allocation_id);
CREATE INDEX idx_time_allocation_week_identifier ON estimate_time_allocation(week_identifier);
CREATE INDEX idx_time_allocation_deleted_at ON estimate_time_allocation(deleted_at);

-- ============================================================================
-- ENRICHMENT TABLES
-- ============================================================================

-- Company enrichment data
CREATE TABLE IF NOT EXISTS company_enrichment (
  id TEXT PRIMARY KEY,
  company_id TEXT NOT NULL,
  
  -- Source information
  source TEXT NOT NULL CHECK(source IN ('abn_lookup', 'clearbit', 'apollo', 'manual', 'other')),
  source_id TEXT, -- External system's ID for this record
  
  -- Enrichment data
  data TEXT NOT NULL, -- JSON containing all enrichment data
  confidence_score REAL DEFAULT 1.0 CHECK(confidence_score >= 0 AND confidence_score <= 1),
  
  -- Timing
  enriched_at TEXT NOT NULL DEFAULT (datetime('now')),
  expires_at TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(expires_at IS NULL OR enriched_at < expires_at)
);

-- Company enrichment indexes
CREATE INDEX idx_company_enrichment_company_id ON company_enrichment(company_id);
CREATE INDEX idx_company_enrichment_source ON company_enrichment(source);
CREATE INDEX idx_company_enrichment_expires_at ON company_enrichment(expires_at);
CREATE INDEX idx_company_enrichment_deleted_at ON company_enrichment(deleted_at);

-- Contact enrichment data
CREATE TABLE IF NOT EXISTS contact_enrichment (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  
  -- Source information
  source TEXT NOT NULL CHECK(source IN ('clearbit', 'apollo', 'linkedin', 'manual', 'other')),
  source_id TEXT,
  
  -- Enrichment data
  data TEXT NOT NULL, -- JSON
  confidence_score REAL DEFAULT 1.0 CHECK(confidence_score >= 0 AND confidence_score <= 1),
  
  -- Timing
  enriched_at TEXT NOT NULL DEFAULT (datetime('now')),
  expires_at TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE SET NULL,
  
  -- Constraints
  CHECK(expires_at IS NULL OR enriched_at < expires_at)
);

-- Contact enrichment indexes
CREATE INDEX idx_contact_enrichment_contact_id ON contact_enrichment(contact_id);
CREATE INDEX idx_contact_enrichment_source ON contact_enrichment(source);
CREATE INDEX idx_contact_enrichment_expires_at ON contact_enrichment(expires_at);
CREATE INDEX idx_contact_enrichment_deleted_at ON contact_enrichment(deleted_at);

-- Enrichment request log
CREATE TABLE IF NOT EXISTS enrichment_log (
  id TEXT PRIMARY KEY,
  
  -- Request details
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact')),
  entity_id TEXT NOT NULL,
  source TEXT NOT NULL,
  
  -- Attempt tracking
  attempted_at TEXT NOT NULL DEFAULT (datetime('now')),
  success INTEGER NOT NULL CHECK(success IN (0, 1)),
  error_message TEXT,
  
  -- Performance metrics
  response_time_ms INTEGER,
  api_credits_used INTEGER,
  
  -- Result summary
  fields_enriched INTEGER,
  confidence_score REAL,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT
);

-- Enrichment log indexes
CREATE INDEX idx_enrichment_log_entity ON enrichment_log(entity_type, entity_id);
CREATE INDEX idx_enrichment_log_source ON enrichment_log(source);
CREATE INDEX idx_enrichment_log_attempted_at ON enrichment_log(attempted_at);
CREATE INDEX idx_enrichment_log_success ON enrichment_log(success);
CREATE INDEX idx_enrichment_log_deleted_at ON enrichment_log(deleted_at);

-- ============================================================================
-- SYSTEM/AUDIT TABLES
-- ============================================================================

-- Activity feed - Comprehensive activity tracking
CREATE TABLE IF NOT EXISTS activity_feed (
  id TEXT PRIMARY KEY,
  
  -- Activity details (matching activity-repository.ts)
  type TEXT NOT NULL,
  subject TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL,
  
  -- Entity references (polymorphic)
  entity_type TEXT CHECK(entity_type IN ('company', 'contact', 'deal', 'project', 'estimate', 'expense', NULL)),
  entity_id TEXT,
  
  -- Dates
  due_date TEXT,
  completed_date TEXT,
  
  -- Related entities (optional)
  company_id TEXT,
  contact_id TEXT,
  deal_id TEXT,
  
  -- Activity metadata
  metadata TEXT DEFAULT '{}', -- JSON with activity-specific data
  is_read INTEGER DEFAULT 0 CHECK(is_read IN (0, 1)),
  importance TEXT DEFAULT 'normal' CHECK(importance IN ('low', 'normal', 'high', 'critical')),
  
  -- Source tracking
  source TEXT CHECK(source IN ('ui', 'api', 'webhook', 'system', 'import', 'xero', 'harvest', 'hubspot')),
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys - SET NULL to preserve activity history
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE SET NULL,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
);

-- Activity feed indexes
CREATE INDEX idx_activity_feed_type ON activity_feed(type);
CREATE INDEX idx_activity_feed_entity ON activity_feed(entity_type, entity_id);
CREATE INDEX idx_activity_feed_company_id ON activity_feed(company_id);
CREATE INDEX idx_activity_feed_contact_id ON activity_feed(contact_id);
CREATE INDEX idx_activity_feed_deal_id ON activity_feed(deal_id);
CREATE INDEX idx_activity_feed_created_at ON activity_feed(created_at);
CREATE INDEX idx_activity_feed_importance ON activity_feed(importance);
CREATE INDEX idx_activity_feed_status ON activity_feed(status);
CREATE INDEX idx_activity_feed_is_read ON activity_feed(is_read);
CREATE INDEX idx_activity_feed_deleted_at ON activity_feed(deleted_at);

-- Field ownership tracking - Which system owns which fields
CREATE TABLE IF NOT EXISTS field_ownership (
  id TEXT PRIMARY KEY,
  
  -- Entity identification
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact', 'deal', 'estimate')),
  entity_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  
  -- Ownership details
  owner_system TEXT NOT NULL CHECK(owner_system IN ('manual', 'hubspot', 'harvest', 'xero', 'estimate')),
  set_at TEXT NOT NULL DEFAULT (datetime('now')),
  set_by TEXT,
  
  -- Override control
  locked INTEGER DEFAULT 0 CHECK(locked IN (0, 1)),
  lock_reason TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  UNIQUE(entity_type, entity_id, field_name)
);

-- Field ownership indexes
CREATE INDEX idx_field_ownership_entity ON field_ownership(entity_type, entity_id);
CREATE INDEX idx_field_ownership_owner_system ON field_ownership(owner_system);
CREATE INDEX idx_field_ownership_deleted_at ON field_ownership(deleted_at);

-- Change log - Detailed field-level change tracking
CREATE TABLE IF NOT EXISTS change_log (
  id TEXT PRIMARY KEY,
  
  -- Entity identification
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  
  -- Change details
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  
  -- Change metadata
  change_type TEXT NOT NULL CHECK(change_type IN ('create', 'update', 'delete', 'restore')),
  changed_at TEXT NOT NULL DEFAULT (datetime('now')),
  changed_by TEXT,
  
  -- Context
  change_source TEXT CHECK(change_source IN ('ui', 'api', 'sync', 'import', 'system')),
  change_reason TEXT,
  batch_id TEXT, -- For grouping related changes
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT
);

-- Change log indexes
CREATE INDEX idx_change_log_entity ON change_log(entity_type, entity_id);
CREATE INDEX idx_change_log_field_name ON change_log(field_name);
CREATE INDEX idx_change_log_changed_at ON change_log(changed_at);
CREATE INDEX idx_change_log_changed_by ON change_log(changed_by);
CREATE INDEX idx_change_log_batch_id ON change_log(batch_id);
CREATE INDEX idx_change_log_deleted_at ON change_log(deleted_at);

-- Team coverage tracking
CREATE TABLE IF NOT EXISTS team_contact_coverage (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  team_member_id TEXT NOT NULL,
  
  -- Coverage details
  coverage_type TEXT NOT NULL CHECK(coverage_type IN ('primary', 'secondary', 'technical', 'executive')),
  strength TEXT CHECK(strength IN ('weak', 'moderate', 'strong')),
  last_interaction_date TEXT,
  interaction_count INTEGER DEFAULT 0,
  
  -- Notes and strategy
  notes TEXT,
  next_action TEXT,
  next_action_date TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
  
  -- Constraints
  UNIQUE(contact_id, team_member_id)
);

-- Team coverage indexes
CREATE INDEX idx_team_coverage_contact_id ON team_contact_coverage(contact_id);
CREATE INDEX idx_team_coverage_team_member_id ON team_contact_coverage(team_member_id);
CREATE INDEX idx_team_coverage_type ON team_contact_coverage(coverage_type);
CREATE INDEX idx_team_coverage_deleted_at ON team_contact_coverage(deleted_at);

-- Opportunity intelligence - AI-generated insights
CREATE TABLE IF NOT EXISTS opportunity_intelligence (
  id TEXT PRIMARY KEY,
  
  -- Target entity
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact', 'deal')),
  entity_id TEXT NOT NULL,
  
  -- Intelligence details
  insight_type TEXT NOT NULL CHECK(insight_type IN ('renewal', 'upsell', 'risk', 'engagement', 'competitive')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  
  -- Scoring and priority
  confidence_score REAL CHECK(confidence_score >= 0 AND confidence_score <= 1),
  priority_score REAL CHECK(priority_score >= 0 AND priority_score <= 100),
  urgency TEXT CHECK(urgency IN ('low', 'medium', 'high', 'critical')),
  
  -- Recommendations
  recommended_actions TEXT DEFAULT '[]', -- JSON array
  potential_value REAL,
  
  -- Tracking
  status TEXT DEFAULT 'active' CHECK(status IN ('active', 'acknowledged', 'dismissed', 'completed')),
  acknowledged_by TEXT,
  acknowledged_at TEXT,
  completed_at TEXT,
  
  -- Validity
  valid_from TEXT NOT NULL DEFAULT (datetime('now')),
  valid_until TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  CHECK(valid_until IS NULL OR valid_from < valid_until)
);

-- Opportunity intelligence indexes
CREATE INDEX idx_opportunity_intel_entity ON opportunity_intelligence(entity_type, entity_id);
CREATE INDEX idx_opportunity_intel_type ON opportunity_intelligence(insight_type);
CREATE INDEX idx_opportunity_intel_status ON opportunity_intelligence(status);
CREATE INDEX idx_opportunity_intel_priority ON opportunity_intelligence(priority_score DESC);
CREATE INDEX idx_opportunity_intel_valid_until ON opportunity_intelligence(valid_until);
CREATE INDEX idx_opportunity_intel_deleted_at ON opportunity_intelligence(deleted_at);

-- ============================================================================
-- INTEGRATION TABLES
-- ============================================================================

-- Harvest invoice cache
CREATE TABLE IF NOT EXISTS harvest_invoice_cache (
  harvest_client_id INTEGER PRIMARY KEY,
  client_name TEXT NOT NULL,
  
  -- Invoice totals
  total_invoiced REAL NOT NULL DEFAULT 0,
  total_paid REAL NOT NULL DEFAULT 0,
  total_outstanding REAL NOT NULL DEFAULT 0,
  
  -- Invoice counts
  invoice_count INTEGER NOT NULL DEFAULT 0,
  overdue_count INTEGER DEFAULT 0,
  
  -- Latest activity
  last_invoice_date TEXT,
  last_payment_date TEXT,
  
  -- Cache metadata
  last_updated TEXT NOT NULL DEFAULT (datetime('now')),
  update_count INTEGER DEFAULT 0,
  cache_version INTEGER DEFAULT 1,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT
);

-- Harvest cache indexes
CREATE INDEX idx_harvest_cache_client_name ON harvest_invoice_cache(client_name);
CREATE INDEX idx_harvest_cache_last_updated ON harvest_invoice_cache(last_updated);
CREATE INDEX idx_harvest_cache_deleted_at ON harvest_invoice_cache(deleted_at);

-- HubSpot sync settings
CREATE TABLE IF NOT EXISTS hubspot_settings (
  id TEXT PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  
  -- Setting metadata
  setting_type TEXT CHECK(setting_type IN ('sync', 'mapping', 'filter', 'config')),
  description TEXT,
  
  -- Validation
  is_encrypted INTEGER DEFAULT 0 CHECK(is_encrypted IN (0, 1)),
  validation_schema TEXT, -- JSON schema for validation
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT
);

-- HubSpot settings indexes
CREATE INDEX idx_hubspot_settings_key ON hubspot_settings(key);
CREATE INDEX idx_hubspot_settings_type ON hubspot_settings(setting_type);
CREATE INDEX idx_hubspot_settings_deleted_at ON hubspot_settings(deleted_at);

-- General application settings
CREATE TABLE IF NOT EXISTS settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  
  -- Setting metadata
  category TEXT,
  description TEXT,
  data_type TEXT CHECK(data_type IN ('string', 'number', 'boolean', 'json')),
  
  -- Validation
  is_public INTEGER DEFAULT 1 CHECK(is_public IN (0, 1)),
  is_encrypted INTEGER DEFAULT 0 CHECK(is_encrypted IN (0, 1)),
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT
);

-- Settings indexes
CREATE INDEX idx_settings_category ON settings(category);
CREATE INDEX idx_settings_is_public ON settings(is_public);
CREATE INDEX idx_settings_deleted_at ON settings(deleted_at);

-- Cashflow snapshots
CREATE TABLE IF NOT EXISTS cashflow_snapshot (
  id TEXT PRIMARY KEY,
  
  -- Snapshot identification
  date TEXT NOT NULL,
  tenant_id TEXT DEFAULT 'default',
  days_ahead INTEGER NOT NULL,
  
  -- Financial data
  opening_balance REAL NOT NULL,
  total_income REAL NOT NULL,
  total_expenses REAL NOT NULL,
  closing_balance REAL NOT NULL,
  
  -- Breakdown by category
  income_breakdown TEXT DEFAULT '{}', -- JSON
  expense_breakdown TEXT DEFAULT '{}', -- JSON
  
  -- Projection details
  included_projections TEXT DEFAULT '[]', -- JSON array of included deal IDs
  scenario TEXT DEFAULT 'expected' CHECK(scenario IN ('worst', 'expected', 'best')),
  
  -- Metadata
  snapshot_type TEXT DEFAULT 'daily' CHECK(snapshot_type IN ('daily', 'weekly', 'monthly')),
  is_forecast INTEGER DEFAULT 0 CHECK(is_forecast IN (0, 1)),
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'system',
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Constraints
  UNIQUE(date, tenant_id, days_ahead)
);

-- Cashflow snapshot indexes
CREATE INDEX idx_cashflow_snapshot_date ON cashflow_snapshot(date);
CREATE INDEX idx_cashflow_snapshot_tenant_id ON cashflow_snapshot(tenant_id);
CREATE INDEX idx_cashflow_snapshot_unique ON cashflow_snapshot(date, tenant_id, days_ahead);
CREATE INDEX idx_cashflow_snapshot_deleted_at ON cashflow_snapshot(deleted_at);

-- ============================================================================
-- VIEWS FOR SOFT-DELETED RECORDS
-- ============================================================================

-- View to track all soft-deleted records across the system
CREATE VIEW IF NOT EXISTS v_soft_deleted_records AS
  SELECT 'company' as table_name, id, name as display_name, deleted_at, updated_by as deleted_by FROM company WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'contact', id, COALESCE(full_name, email) as display_name, deleted_at, updated_by as deleted_by FROM contact WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'deal', id, name, deleted_at, updated_by as deleted_by FROM deal WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'estimate', id, client_name || ' - ' || COALESCE(project_name, 'Untitled'), deleted_at, updated_by as deleted_by FROM estimate WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'project', id, name, deleted_at, updated_by as deleted_by FROM project WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'expense', id, description, deleted_at, updated_by as deleted_by FROM expense WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'note', id, SUBSTR(content, 1, 50) || CASE WHEN LENGTH(content) > 50 THEN '...' ELSE '' END, deleted_at, updated_by as deleted_by FROM note WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'tender', id, title, deleted_at, updated_by as deleted_by FROM tender WHERE deleted_at IS NOT NULL
  ORDER BY deleted_at DESC;

-- Active record views (excluding soft-deleted)
CREATE VIEW IF NOT EXISTS v_active_companies AS
  SELECT * FROM company WHERE deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS v_active_contacts AS
  SELECT * FROM contact WHERE deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS v_active_deals AS
  SELECT * FROM deal WHERE deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS v_active_estimates AS
  SELECT * FROM estimate WHERE deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS v_active_projects AS
  SELECT * FROM project WHERE deleted_at IS NULL;

-- ============================================================================
-- TRIGGERS FOR AUDIT TIMESTAMPS
-- ============================================================================

-- Update timestamp triggers for all tables
CREATE TRIGGER update_company_timestamp AFTER UPDATE ON company
BEGIN
  UPDATE company SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_contact_timestamp AFTER UPDATE ON contact
BEGIN
  UPDATE contact SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_deal_timestamp AFTER UPDATE ON deal
BEGIN
  UPDATE deal SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_estimate_timestamp AFTER UPDATE ON estimate
BEGIN
  UPDATE estimate SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_project_timestamp AFTER UPDATE ON project
BEGIN
  UPDATE project SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_expense_timestamp AFTER UPDATE ON expense
BEGIN
  UPDATE expense SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_note_timestamp AFTER UPDATE ON note
BEGIN
  UPDATE note SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_tender_timestamp AFTER UPDATE ON tender
BEGIN
  UPDATE tender SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER update_radar_action_items_timestamp AFTER UPDATE ON radar_action_items
BEGIN
  UPDATE radar_action_items SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- ============================================================================
-- INITIAL SETTINGS AND CONFIGURATION
-- ============================================================================

-- Insert default settings
INSERT OR IGNORE INTO settings (key, value, category, description, data_type) VALUES
  ('app_version', '2.0.0', 'system', 'Application version', 'string'),
  ('schema_version', '000', 'system', 'Database schema version', 'string'),
  ('default_currency', 'AUD', 'financial', 'Default currency for financial calculations', 'string'),
  ('default_payment_terms', '30', 'financial', 'Default payment terms in days', 'number'),
  ('fiscal_year_start', '07-01', 'financial', 'Fiscal year start date (MM-DD)', 'string'),
  ('enable_soft_delete', 'true', 'system', 'Enable soft delete functionality', 'boolean'),
  ('cache_ttl_seconds', '21600', 'performance', 'Default cache TTL in seconds (6 hours)', 'number'),
  ('max_api_retries', '3', 'integration', 'Maximum API retry attempts', 'number'),
  ('log_retention_days', '90', 'system', 'Log retention period in days', 'number');

-- Record this migration
INSERT OR IGNORE INTO migrations (id, name, executed_by) VALUES 
  ('000', 'unified_schema', 'system');

-- Radar Action Items - For tracking action items on companies needing investigation
CREATE TABLE IF NOT EXISTS radar_action_items (
  id TEXT PRIMARY KEY,
  company_id TEXT NOT NULL,
  
  -- Action details
  action_type TEXT NOT NULL CHECK(action_type IN ('research', 'contact', 'qualify', 'analyze', 'follow_up', 'other')),
  title TEXT NOT NULL,
  description TEXT,
  
  -- Assignment and status
  assigned_to TEXT NOT NULL, -- User email or ID
  status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'in_progress', 'completed', 'cancelled')),
  priority TEXT DEFAULT 'normal' CHECK(priority IN ('low', 'normal', 'high', 'urgent')),
  
  -- Timing
  due_date TEXT,
  started_at TEXT,
  completed_at TEXT,
  
  -- Additional context
  notes TEXT,
  completion_notes TEXT,
  
  -- Audit fields
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT NOT NULL,
  updated_by TEXT,
  deleted_at TEXT,
  
  -- Foreign keys
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_radar_action_items_company_id ON radar_action_items(company_id);
CREATE INDEX idx_radar_action_items_assigned_to ON radar_action_items(assigned_to);
CREATE INDEX idx_radar_action_items_status ON radar_action_items(status);
CREATE INDEX idx_radar_action_items_due_date ON radar_action_items(due_date);
CREATE INDEX idx_radar_action_items_deleted_at ON radar_action_items(deleted_at);

-- ============================================================================
-- END OF UNIFIED SCHEMA
-- ============================================================================

-- ===========================================
-- Schema Version Tracking
-- ===========================================
CREATE TABLE IF NOT EXISTS schema_version (
  version INTEGER PRIMARY KEY,
  description TEXT,
  applied_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Mark this as version 1
INSERT OR IGNORE INTO schema_version (version, description) 
VALUES (1, 'Initial unified schema with 31 tables including radar_action_items');
