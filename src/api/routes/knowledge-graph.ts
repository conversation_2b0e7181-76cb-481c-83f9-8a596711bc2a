import express from 'express';
import { KnowledgeGraphRepository } from '../repositories/knowledge-graph-repository';

const router = express.Router();
const knowledgeGraphRepo = new KnowledgeGraphRepository();

/**
 * @route GET /api/knowledge-graph
 * @desc Get the complete knowledge graph data
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    const {
      includeDeleted = 'false',
      entityTypes = 'company,contact,deal,project',
      maxNodes = '1000',
      // New filter parameters
      searchTerm = '',
      minNodeDegree = '0',
      linkTypes = '',
      page = '1',
      pageSize = '1000'
    } = req.query;

    // Parse query parameters with validation
    const options = {
      includeDeleted: includeDeleted === 'true',
      entityTypes: (entityTypes as string).split(',').filter(Boolean) as any[],
      maxNodes: Math.min(parseInt(maxNodes as string, 10) || 1000, 5000), // Cap at 5000
      // New filter options
      searchTerm: (searchTerm as string).trim(),
      minNodeDegree: Math.max(0, parseInt(minNodeDegree as string, 10) || 0),
      linkTypes: linkTypes ? (linkTypes as string).split(',').filter(Boolean) : [],
      pagination: {
        page: Math.max(1, parseInt(page as string, 10) || 1),
        pageSize: Math.min(parseInt(pageSize as string, 10) || 1000, 2000) // Cap at 2000
      }
    };

    // Get filtered knowledge graph data
    const graphData = await knowledgeGraphRepo.getKnowledgeGraph(options);

    res.json({
      success: true,
      data: graphData
    });
  } catch (error) {
    console.error('Error fetching knowledge graph:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch knowledge graph',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route GET /api/knowledge-graph/:entityId
 * @desc Get subgraph centered on a specific entity
 * @access Private
 */
router.get('/:entityId', async (req, res) => {
  try {
    const { entityId } = req.params;
    const {
      entityType = 'company',
      depth = '2'
    } = req.query;

    // Get entity subgraph
    const graphData = await knowledgeGraphRepo.getEntitySubgraph(
      entityId,
      entityType as any,
      parseInt(depth as string, 10) || 2
    );

    res.json({
      success: true,
      data: graphData
    });
  } catch (error) {
    console.error('Error fetching entity subgraph:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch entity subgraph',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;