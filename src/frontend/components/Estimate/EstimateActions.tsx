import React from "react";
import { getRetryableErrorMessage } from "../../utils/error-helpers";

// Define props based on what the component needs from hooks/parent
interface EstimateActionsProps {
  isEstimateInitialized: boolean;
  isSaving: boolean;
  isSavedSuccessfully: boolean;
  saveError: string | null;
  // saveSuccessMessage prop removed as it's not used here
  draftUuid: string | null;
  canSaveEstimate: boolean;
  isLoadingPermissions: boolean; // To disable buttons while checking permissions
  hasStaffAllocations: boolean; // To disable Harvest save if no allocations
  onInitialize: () => void;
  onReset: () => void;
  onSaveDraft: () => Promise<boolean>;
  onSaveToHarvest: () => Promise<boolean>;
  // Need form state validation status to disable Initialize button correctly
  isFormValidForInit: boolean;
  isLoadingClients: boolean; // Also needed to disable Initialize button
}

const EstimateActions: React.FC<EstimateActionsProps> = ({
  isEstimateInitialized,
  isSaving,
  isSavedSuccessfully,
  saveError,
  // saveSuccessMessage prop removed
  draftUuid,
  canSaveEstimate,
  isLoadingPermissions,
  hasStaffAllocations,
  onInitialize,
  onReset,
  onSaveDraft,
  onSaveToHarvest,
  isFormValidForInit,
  isLoadingClients,
}) => {
  // State for help tooltip
  const [showHelp, setShowHelp] = React.useState(false);

  return (
    <div className="w-[25%]">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 h-full">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Estimate Actions
            </h3>
            {/* Help icon with tooltip */}
            <div className="relative ml-1">
              <button
                className="w-4 h-4 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 text-xs hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors focus:outline-none"
                onClick={() => setShowHelp(!showHelp)}
                aria-label="Help information"
              >
                ?
              </button>
              {showHelp && (
                <div className="absolute z-50 bottom-6 -left-4 w-60 px-3 py-2 bg-white dark:bg-gray-700 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 text-xs text-gray-600 dark:text-gray-300">
                  <div className="flex justify-between items-start mb-1">
                    <strong className="text-gray-700 dark:text-gray-200">
                      Estimate Actions
                    </strong>
                    <button
                      onClick={() => setShowHelp(false)}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      ×
                    </button>
                  </div>
                  <ul className="list-disc pl-4 space-y-1 mb-1.5">
                    <li>
                      <strong>Initialise</strong>: Start a new estimate after
                      setting client and dates
                    </li>
                    <li>
                      <strong>Clear</strong>: Reset all data and start over
                    </li>
                    <li>
                      <strong>Save Draft</strong>: Store your estimate in
                      Upstream to continue later
                    </li>
                    <li>
                      <strong>Create in Harvest</strong>: Create estimate in
                      Harvest
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
          {/* Status indicators - kept here for now, could be moved */}
          {isSavedSuccessfully && (
            <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
              <svg
                className="w-3 h-3 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              Saved
            </span>
          )}
          {saveError && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-2">
              <div className="flex items-start">
                <svg
                  className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 mr-2 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div className="text-sm text-red-700 dark:text-red-300">
                  {(() => {
                    try {
                      const errorObj =
                        typeof saveError === "string"
                          ? JSON.parse(saveError)
                          : saveError;
                      return getRetryableErrorMessage(errorObj, "save");
                    } catch {
                      return getRetryableErrorMessage(saveError, "save");
                    }
                  })()}
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col justify-between h-[calc(100%-2rem)]">
          {!isEstimateInitialized ? (
            <button
              onClick={onInitialize}
              className="w-full px-3 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 transition-colors duration-200 font-medium text-sm shadow-sm"
              disabled={!isFormValidForInit || isLoadingClients} // Disable based on form validity and client loading
            >
              Initialise Estimate
            </button>
          ) : (
            <div className="flex flex-col h-full justify-between py-1">
              {/* Top row: Reset and Create in Harvest */}
              <div className="flex gap-2">
                <button
                  onClick={onReset}
                  className="w-1/2 px-3 py-2.5 bg-white text-red-600 border border-red-600 rounded-lg hover:bg-red-50 text-[12px] font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isSaving || isSavedSuccessfully} // Disable if saving or already saved
                >
                  Clear
                </button>
                <button
                  onClick={onSaveToHarvest}
                  className="w-1/2 px-3 py-2.5 bg-[#f36c21] text-white rounded-lg hover:bg-[#e05a10] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-1 text-[12px] font-medium"
                  disabled={
                    isSaving ||
                    isSavedSuccessfully ||
                    !hasStaffAllocations || // Disable if no staff/time allocated
                    isLoadingPermissions || // Disable while checking permissions
                    !canSaveEstimate // Disable if user lacks permission
                  }
                  title={
                    !isLoadingPermissions && !canSaveEstimate
                      ? "You lack permission to save estimates"
                      : undefined
                  }
                >
                  {/* Saving/Saved/Create text */}
                  {isSaving ? (
                    <>
                      <svg
                        className="animate-spin h-3 w-3 mr-1"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Saving...
                    </>
                  ) : isSavedSuccessfully ? (
                    <>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 mr-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Saved
                    </>
                  ) : (
                    <span className="text-[12px]">Create in Harvest</span>
                  )}
                </button>
              </div>
              {/* Save Draft Button at bottom */}
              <button
                onClick={onSaveDraft}
                className="w-full px-3 py-2.5 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed text-[12px] font-medium"
                disabled={isSaving || isSavedSuccessfully || !canSaveEstimate} // Disable if saving, saved, or no permission
              >
                {/* Saving/Update/Save text */}
                {isSaving && !isSavedSuccessfully ? (
                  <div className="flex items-center justify-center">
                    <svg
                      className="animate-spin h-3 w-3 mr-1"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Saving...
                  </div>
                ) : draftUuid ? (
                  "Update Draft"
                ) : (
                  "Save Draft"
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EstimateActions;
