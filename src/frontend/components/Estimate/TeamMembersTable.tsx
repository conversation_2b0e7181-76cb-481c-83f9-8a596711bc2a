import React, { useState, useEffect, useCallback } from "react";
import {
  AllocationWithTotals,
  ProjectTotals,
} from "../../hooks/useEstimateStaffManagement";
import { formatCurrency, formatRateDisplay } from "./utils";
import { RateDisplayMode, HoursPerDay } from "./RateDisplayControls";

interface TeamMembersTableProps {
  /**
   * Staff allocations with calculated totals
   */
  allocationsWithTotals: AllocationWithTotals[];

  /**
   * Project total calculations
   */
  projectTotals: ProjectTotals;

  /**
   * Handler for changing staff rate
   */
  onRateChange: (internalId: string, newRate: number) => void;

  /**
   * Handler for triggering staff add modal
   */
  onTriggerAddStaff: () => void;

  /**
   * Handler for removing staff
   */
  onRemoveStaff: (internalId: string) => void;

  /**
   * Whether the table is in read-only mode
   */
  isReadOnly?: boolean;

  /**
   * Rate display mode (daily or hourly)
   */
  rateDisplayMode?: RateDisplayMode;

  /**
   * Hours per day for rate calculations
   */
  hoursPerDay?: HoursPerDay;
}

/**
 * Displays a table of team members with their rates, costs, and financial metrics
 */
const TeamMembersTable: React.FC<TeamMembersTableProps> = ({
  allocationsWithTotals,
  projectTotals,
  onRateChange,
  onTriggerAddStaff,
  onRemoveStaff,
  isReadOnly = false,
  rateDisplayMode = "daily",
  hoursPerDay = 7.5,
}) => {
  // Local state to track input values while user is typing
  const [inputValues, setInputValues] = useState<Record<string, string>>({});

  // Determine display labels based on rate display mode
  const rateLabel = rateDisplayMode === "hourly" ? "Hourly" : "Daily";
  const showHourly = rateDisplayMode === "hourly";

  // Helper function to get display value for an input
  const getDisplayValue = (staff: any) => {
    // If user is currently editing this field, show their input
    if (inputValues[staff.internalId] !== undefined) {
      return inputValues[staff.internalId];
    }
    // Show the rate based on pricing model
    if (showHourly) {
      // In hourly pricing mode, convert daily rate to hourly for display
      return (staff.rateProposedDaily / hoursPerDay).toFixed(2);
    } else {
      // In daily pricing mode, show daily rate directly
      return staff.rateProposedDaily.toString();
    }
  };

  // Handle input change (just track what user types)
  const handleInputChange = (internalId: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [internalId]: value }));
  };

  // Handle input blur (validate and save)
  const handleInputBlur = (internalId: string, value: string) => {
    const numericValue = parseFloat(value) || 0;

    // In hourly pricing mode, convert hourly input to daily rate for storage
    // In daily pricing mode, store the daily rate directly
    const dailyRate = showHourly ? numericValue * hoursPerDay : numericValue;

    onRateChange(internalId, dailyRate);

    // Clear the local input state
    setInputValues((prev) => {
      const newState = { ...prev };
      delete newState[internalId];
      return newState;
    });
  };
  return (
    <div className="w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-semibold text-gray-800 dark:text-gray-200">
          Team Members & Rates
        </h3>
        {!isReadOnly && (
          <button
            onClick={onTriggerAddStaff}
            className="inline-flex items-center px-3 py-1.5 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-sm text-secondary hover:text-secondary-dark dark:text-secondary-light dark:hover:text-secondary hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg
              className="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              ></path>
            </svg>
            Add Staff Member
          </button>
        )}
      </div>

      {allocationsWithTotals.length === 0 ? (
        <div className="text-center py-8 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
          <svg
            className="w-10 h-10 mx-auto text-gray-400 mb-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
          <h4 className="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">
            No Staff Assigned
          </h4>
          <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
            Add staff members to your estimate to begin allocating time and
            calculating project costs.
          </p>
          {!isReadOnly && (
            <button
              onClick={onTriggerAddStaff}
              className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 transition-colors duration-200 font-medium text-sm shadow-sm"
            >
              Add Staff Member
            </button>
          )}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full table-fixed">
            <colgroup>
              <col className="w-[15%]" />
              <col className="w-[15%]" />
              <col className="w-[10%]" />
              <col className="w-[10%]" />
              <col className="w-[10%]" />
              <col className="w-[12%]" />
              <col className="w-[12%]" />
              <col className="w-[8%]" />
              <col className="w-[8%]" />
            </colgroup>
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-700">
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Team Member
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Target Rate{" "}
                  <span className="font-normal text-gray-400 dark:text-gray-500">
                    ({rateLabel})
                  </span>
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Proposed Rate{" "}
                  <span className="font-normal text-gray-400 dark:text-gray-500">
                    ({rateLabel})
                  </span>
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Days
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Cost
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Fees
                </th>
                <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  GM %
                </th>
                <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {allocationsWithTotals.map((staff) => {
                const marginPercentage =
                  staff.totalFees > 0
                    ? ((staff.totalFees - staff.totalCost) / staff.totalFees) *
                      100
                    : 0;

                return (
                  <tr
                    key={staff.internalId}
                    className={`hover:bg-gray-50 dark:hover:bg-gray-700/50 ${
                      staff.isPlaceholder
                        ? "bg-gray-50/50 dark:bg-gray-700/20"
                        : ""
                    }`}
                  >
                    <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 font-medium">
                      {staff.firstName} {staff.lastName || ""}
                      {staff.isPlaceholder && (
                        <span className="text-xs text-gray-500 italic ml-1">
                          (placeholder)
                        </span>
                      )}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
                      {staff.projectRole || "Staff"}
                    </td>
                    <td className="px-3 py-2 text-center text-sm text-gray-600 dark:text-gray-400">
                      {formatRateDisplay(
                        staff.onbordTargetRateDaily,
                        showHourly,
                        hoursPerDay
                      )}
                    </td>
                    <td className="px-3 py-2 text-center">
                      {isReadOnly ? (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {formatRateDisplay(
                            staff.rateProposedDaily,
                            showHourly,
                            hoursPerDay
                          )}
                        </span>
                      ) : (
                        <div className="relative inline-block">
                          <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 pointer-events-none">
                            $
                          </span>
                          <input
                            type="text"
                            inputMode="decimal"
                            value={getDisplayValue(staff)}
                            onChange={(e) =>
                              handleInputChange(
                                staff.internalId,
                                e.target.value
                              )
                            }
                            onBlur={(e) =>
                              handleInputBlur(staff.internalId, e.target.value)
                            }
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.currentTarget.blur();
                              }
                            }}
                            className="w-24 py-1 px-2 pl-6 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-sm text-gray-700 dark:text-gray-300 appearance-none"
                          />
                        </div>
                      )}
                    </td>
                    <td className="px-3 py-2 text-center text-sm text-gray-700 dark:text-gray-300 font-medium">
                      {staff.totalDays.toFixed(1)}
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-red-600 dark:text-red-400">
                      {formatCurrency(staff.totalCost)}
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-green-600 dark:text-green-400">
                      {formatCurrency(staff.totalFees)}
                    </td>
                    <td className="px-3 py-2 text-right">
                      <span
                        className={`text-sm ${
                          marginPercentage >= 30
                            ? "text-green-600 dark:text-green-400"
                            : marginPercentage >= 20
                            ? "text-amber-500 dark:text-amber-400"
                            : "text-red-600 dark:text-red-400"
                        }`}
                      >
                        {marginPercentage.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-3 py-2 text-right text-sm text-gray-500 dark:text-gray-400">
                      {!isReadOnly && (
                        <button
                          onClick={() => onRemoveStaff(staff.internalId)}
                          className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          title="Remove staff member"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                        </button>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
            {allocationsWithTotals.length > 0 && (
              <tfoot className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <td
                    colSpan={2}
                    className="px-3 py-2 text-sm font-medium text-right text-gray-800 dark:text-gray-200"
                  >
                    Totals
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-gray-700 dark:text-gray-300">
                    {/* Average target rate, weighted by days */}
                    {projectTotals.totalDays > 0
                      ? formatRateDisplay(
                          projectTotals.totalTargetRevenue /
                            projectTotals.totalDays,
                          showHourly,
                          hoursPerDay
                        )
                      : formatRateDisplay(0, showHourly, hoursPerDay)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-gray-700 dark:text-gray-300">
                    {/* Average proposed rate, weighted by days */}
                    {projectTotals.totalDays > 0
                      ? formatRateDisplay(
                          projectTotals.totalRevenue / projectTotals.totalDays,
                          showHourly,
                          hoursPerDay
                        )
                      : formatRateDisplay(0, showHourly, hoursPerDay)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-center text-gray-700 dark:text-gray-300">
                    {projectTotals.totalDays.toFixed(1)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                    {formatCurrency(projectTotals.totalCost)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                    {formatCurrency(projectTotals.totalRevenue)}
                  </td>
                  <td className="px-3 py-2 text-sm font-medium text-right">
                    <span
                      className={`${
                        projectTotals.marginPercentage >= 30
                          ? "text-green-600 dark:text-green-400"
                          : projectTotals.marginPercentage >= 20
                          ? "text-amber-500 dark:text-amber-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-3 py-2"></td>
                </tr>
              </tfoot>
            )}
          </table>
        </div>
      )}
    </div>
  );
};

export default TeamMembersTable;
