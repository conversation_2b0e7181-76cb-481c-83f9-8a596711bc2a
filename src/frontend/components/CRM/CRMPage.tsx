import React from "react";
import { useLocation } from "react-router-dom";
import CRMDashboard from "./CRMDashboard";

/**
 * Main CRM page component
 * This is the entry point for the CRM feature
 */
const CRMPage: React.FC = () => {
  const location = useLocation();

  // Extract the current path to use as a key to force re-rendering when the route changes
  const currentPath = location.pathname;

  // We're going to rely on the CRMDashboard component to handle the routing
  // based on the path. The CRMDashboard will return null for deal detail pages.
  return (
    <div className="space-y-6 overflow-visible overflow-x-hidden min-h-0">
      {/* Header with title */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
          Customer Relationship Management
        </h1>
      </div>

      {/* CRM Dashboard - key forces re-render when path changes */}
      <CRMDashboard key={currentPath} />
    </div>
  );
};

export default CRMPage;
