import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useQuery } from 'react-query';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  BuildingOffice2Icon, 
  CurrencyDollarIcon,
  FunnelIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  QuestionMarkCircleIcon,
  UserIcon,
  CalendarIcon,
  TagIcon,
  BuildingStorefrontIcon,
  MapPinIcon,
  ChevronUpDownIcon,
  ClockIcon,
  LinkIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { getCompanies, getContacts, getDeals } from '../../../api/crm';
import type { Company, Contact, Deal } from '../../../types/crm-types';
import SimpleTooltip, { TooltipData } from '../../common/SimpleTooltip';
import Badge from '../../shared/Badge';
import { CommandPalette } from '../shared/CommandPalette';
import RecentSearches from './RecentSearches';
import EntityPreview from './EntityPreview';
import { useSearchPreferences } from './hooks/useSearchPreferences';
import DraggableEntity, { DragData } from '../shared/DraggableEntity';
import AssociationModal, { AssociationType } from '../shared/AssociationModal';
import { useEntityPreview } from './hooks/useEntityPreview';
import { highlightText, matchesSearch } from './utils/highlightText';
import { sortItems, type SortConfig, type SortField } from './utils/sorting';

type EntityType = 'all' | 'contacts' | 'companies' | 'deals';

interface FilterState {
  entityType: EntityType;
  dealStage?: string[];
  contactRole?: string[];
  companyIndustry?: string[];
  dateRange?: { from: string; to: string };
  linkedStatus?: 'both' | 'hubspot_only' | 'harvest_only' | 'none' | null;
  hasEstimates?: boolean;
  minDealValue?: number;
  maxDealValue?: number;
  quickFilter?: 'recently-updated' | 'high-value' | 'unlinked' | 'my-items' | null;
}

interface UnifiedSearchProps {
  onSelectEntity: (type: string, id: string) => void;
}

export const UnifiedSearch: React.FC<UnifiedSearchProps> = ({ onSelectEntity }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isCommandPaletteOpen, setIsCommandPaletteOpen] = useState(false);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    content: '',
    x: 0,
    y: 0
  });
  
  // Track how many items to show for each entity type
  const [visibleCounts, setVisibleCounts] = useState({
    contacts: 1000, // Show all contacts
    companies: 1000, // Show all companies
    deals: 1000
  });

  // Association modal state
  const [showAssociationModal, setShowAssociationModal] = useState(false);
  const [associationData, setAssociationData] = useState<{
    sourceEntity: Contact | Company;
    targetEntity: Contact | Company;
    associationType: AssociationType;
  } | null>(null);
  
  // Debug effect
  useEffect(() => {
    console.log('UnifiedSearch: Association modal state changed', {
      showAssociationModal,
      hasAssociationData: !!associationData,
      associationData: associationData ? {
        sourceId: associationData.sourceEntity?.id,
        targetId: associationData.targetEntity?.id,
        type: associationData.associationType
      } : null
    });
  }, [showAssociationModal, associationData]);
  
  // Track dragging state to disable hover previews
  const [isDraggingGlobally, setIsDraggingGlobally] = useState(false);
  
  // Use search preferences hook
  const {
    recentSearches,
    addRecentSearch,
    clearRecentSearches
  } = useSearchPreferences();
  
  // Use entity preview hook
  const { preview, showPreview, hidePreview, setHoveringPreview } = useEntityPreview(500);
  
  // Initialize filters from URL
  const [filters, setFilters] = useState<FilterState>(() => {
    const params = new URLSearchParams(location.search);
    return {
      entityType: (params.get('type') as EntityType) || 'all',
      dealStage: params.get('dealStage')?.split(',').filter(Boolean),
      contactRole: params.get('contactRole')?.split(',').filter(Boolean),
      companyIndustry: params.get('companyIndustry')?.split(',').filter(Boolean),
      dateRange: params.get('dateFrom') && params.get('dateTo') 
        ? { from: params.get('dateFrom')!, to: params.get('dateTo')! }
        : undefined,
      linkedStatus: params.get('linkedStatus') as FilterState['linkedStatus'],
      hasEstimates: params.get('hasEstimates') === 'true',
      minDealValue: params.get('minDealValue') ? Number(params.get('minDealValue')) : undefined,
      maxDealValue: params.get('maxDealValue') ? Number(params.get('maxDealValue')) : undefined,
      quickFilter: params.get('quickFilter') as FilterState['quickFilter'],
    };
  });


  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    
    if (searchTerm) params.set('search', searchTerm);
    if (filters.entityType !== 'all') params.set('type', filters.entityType);
    if (filters.dealStage?.length) params.set('dealStage', filters.dealStage.join(','));
    if (filters.contactRole?.length) params.set('contactRole', filters.contactRole.join(','));
    if (filters.companyIndustry?.length) params.set('companyIndustry', filters.companyIndustry.join(','));
    if (filters.dateRange) {
      params.set('dateFrom', filters.dateRange.from);
      params.set('dateTo', filters.dateRange.to);
    }
    if (filters.linkedStatus) params.set('linkedStatus', filters.linkedStatus);
    if (filters.hasEstimates) params.set('hasEstimates', 'true');
    if (filters.minDealValue) params.set('minDealValue', filters.minDealValue.toString());
    if (filters.maxDealValue) params.set('maxDealValue', filters.maxDealValue.toString());
    if (filters.quickFilter) params.set('quickFilter', filters.quickFilter);
    
    const newSearch = params.toString();
    if (newSearch !== location.search.slice(1)) {
      navigate(`${location.pathname}${newSearch ? `?${newSearch}` : ''}`, { replace: true });
    }
  }, [filters, searchTerm, navigate, location]);

  // Fetch all data with proper caching
  const { data: rawCompanies = [] } = useQuery('companies', getCompanies, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });
  const { data: rawContacts = [] } = useQuery('contacts', getContacts, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });
  const { data: rawDeals = [] } = useQuery('deals', getDeals, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false
  });
  
  // Filter out entities with null/undefined IDs to prevent React key warnings
  const companies = useMemo(() => rawCompanies.filter(c => c && c.id), [rawCompanies]);
  const contacts = useMemo(() => rawContacts.filter(c => c && c.id), [rawContacts]);
  const deals = useMemo(() => rawDeals.filter(d => d && d.id), [rawDeals]);

  // Extract unique values for filter options
  const filterOptions = useMemo(() => {
    const dealStages = [...new Set(deals.map(d => d.stage).filter(Boolean))];
    const contactRoles = [...new Set(contacts.flatMap(c => c.roles || []).filter(Boolean))];
    const industries = [...new Set(companies.map(c => c.industry).filter(Boolean))];
    
    return { dealStages, contactRoles, industries };
  }, [deals, contacts, companies]);

  // Apply advanced filters
  const applyFilters = useCallback((items: any[], type: 'contacts' | 'companies' | 'deals') => {
    return items.filter(item => {
      // Quick filter presets
      if (filters.quickFilter) {
        const now = new Date();
        const itemDate = new Date(item.updated_at || item.updatedAt || item.created_at || item.createdAt);
        
        switch (filters.quickFilter) {
          case 'recently-updated':
            const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            if (itemDate < oneWeekAgo) return false;
            break;
          
          case 'high-value':
            if (type !== 'deals' || (item.value || 0) < 50000) return false;
            break;
          
          case 'unlinked':
            if (type === 'companies' && item.linkedStatus !== 'none') return false;
            if (type !== 'companies') return false;
            break;
          
          case 'my-items':
            // This would need user context - for now just return all
            // In a real implementation, filter by assignedTo === currentUser.id
            break;
        }
      }

      // Date range filter
      if (filters.dateRange) {
        const itemDate = new Date(item.created_at || item.createdAt);
        const fromDate = new Date(filters.dateRange.from);
        const toDate = new Date(filters.dateRange.to);
        if (itemDate < fromDate || itemDate > toDate) return false;
      }

      // Type-specific filters
      if (type === 'deals') {
        if (filters.dealStage?.length && !filters.dealStage.includes(item.stage)) return false;
        if (filters.hasEstimates && !item.hasEstimates) return false;
        if (filters.minDealValue && item.value < filters.minDealValue) return false;
        if (filters.maxDealValue && item.value > filters.maxDealValue) return false;
      }

      if (type === 'contacts') {
        if (filters.contactRole?.length) {
          const hasRole = filters.contactRole.some(role => item.roles?.includes(role));
          if (!hasRole) return false;
        }
      }

      if (type === 'companies') {
        if (filters.companyIndustry?.length && !filters.companyIndustry.includes(item.industry)) {
          return false;
        }
        if (filters.linkedStatus && item.linkedStatus !== filters.linkedStatus) return false;
      }

      return true;
    });
  }, [filters]);

  // Unified search results with filters
  const searchResults = useMemo(() => {
    const term = searchTerm.toLowerCase();
    
    // First filter by search term
    const searchFiltered = {
      contacts: term ? contacts.filter(c => {
        const fullName = `${c?.firstName || ''} ${c?.lastName || ''}`.toLowerCase();
        const email = c?.email?.toLowerCase() || '';
        const phone = c?.phone?.toLowerCase() || '';
        return fullName.includes(term) || email.includes(term) || phone.includes(term);
      }) : contacts,
      companies: term ? companies.filter(c => {
        const name = c?.name?.toLowerCase() || '';
        const industry = c?.industry?.toLowerCase() || '';
        return name.includes(term) || industry.includes(term);
      }) : companies,
      deals: term ? deals.filter(d => {
        const name = d?.name?.toLowerCase() || '';
        const companyName = typeof d?.company === 'string' 
          ? d.company.toLowerCase()
          : (d?.company?.name?.toLowerCase() || '');
        return name.includes(term) || companyName.includes(term);
      }) : deals
    };

    // Then apply advanced filters
    const filtered = {
      contacts: applyFilters(searchFiltered.contacts, 'contacts'),
      companies: applyFilters(searchFiltered.companies, 'companies'),
      deals: applyFilters(searchFiltered.deals, 'deals')
    };

    // Apply sorting
    const sorted = {
      contacts: sortItems(filtered.contacts, sortConfig, 'contacts'),
      companies: sortItems(filtered.companies, sortConfig, 'companies'),
      deals: sortItems(filtered.deals, sortConfig, 'deals')
    };

    if (filters.entityType === 'all') {
      return sorted;
    }

    return {
      contacts: filters.entityType === 'contacts' ? sorted.contacts : [],
      companies: filters.entityType === 'companies' ? sorted.companies : [],
      deals: filters.entityType === 'deals' ? sorted.deals : []
    };
  }, [searchTerm, filters, contacts, companies, deals, applyFilters, sortConfig]);

  const totalResults = 
    searchResults.contacts.length + 
    searchResults.companies.length; 
    // + searchResults.deals.length; // Deals hidden

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.dealStage?.length) count += filters.dealStage.length;
    if (filters.contactRole?.length) count += filters.contactRole.length;
    if (filters.companyIndustry?.length) count += filters.companyIndustry.length;
    if (filters.dateRange) count++;
    if (filters.linkedStatus) count++;
    if (filters.hasEstimates) count++;
    if (filters.minDealValue || filters.maxDealValue) count++;
    if (filters.quickFilter) count++;
    return count;
  }, [filters]);

  // CSV Export functionality
  const exportToCSV = useCallback(() => {
    const csvRows = [];
    
    // Header
    csvRows.push(['Type', 'Name', 'Email/Stage/Industry', 'Phone/Value/Website', 'Created', 'Additional Info']);
    
    // Contacts
    searchResults.contacts.forEach(contact => {
      csvRows.push([
        'Contact',
        `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed',
        contact.email || '',
        contact.phone || '',
        new Date(contact.created_at || contact.createdAt).toLocaleDateString(),
        (typeof contact.company === 'string' ? contact.company : contact.company?.name) || ''
      ]);
    });
    
    // Companies
    searchResults.companies.forEach(company => {
      csvRows.push([
        'Company',
        company.name || 'Unnamed',
        company.industry || '',
        company.website || '',
        new Date(company.created_at || company.createdAt).toLocaleDateString(),
        company.linkedStatus || ''
      ]);
    });
    
    // Deals
    searchResults.deals.forEach(deal => {
      csvRows.push([
        'Deal',
        deal.name || 'Untitled',
        deal.stage || '',
        `$${(deal.value || 0).toLocaleString()}`,
        new Date(deal.created_at || deal.createdAt).toLocaleDateString(),
        `${deal.probability || 0}% probability`
      ]);
    });
    
    // Convert to CSV string
    const csvContent = csvRows
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n');
    
    // Download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `crm-directory-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    URL.revokeObjectURL(link.href);
  }, [searchResults]);


  // Track search when user searches
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm) {
        const totalResults = 
          searchResults.contacts.length + 
          searchResults.companies.length + 
          searchResults.deals.length;
        addRecentSearch(searchTerm, totalResults);
      }
    }, 1000); // Debounce to avoid too many saves

    return () => clearTimeout(timer);
  }, [searchTerm, searchResults, addRecentSearch]);

  // Reset visible counts when search or filters change
  useEffect(() => {
    setVisibleCounts({
      contacts: 1000, // Show all
      companies: 1000, // Show all
      deals: 1000
    });
  }, [searchTerm, filters]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for command palette
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsCommandPaletteOpen(true);
      }
      // Cmd/Ctrl + F for filter panel
      if ((e.metaKey || e.ctrlKey) && e.key === 'f' && !e.shiftKey) {
        e.preventDefault();
        setShowFilters(!showFilters);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showFilters]);

  // Drag and drop handlers
  const handleDrop = useCallback((draggedData: DragData, targetEntity: Contact | Company, targetType: 'contact' | 'company') => {
    const { entity: sourceEntity, entityType: sourceType } = draggedData;
    
    console.log('UnifiedSearch: handleDrop called', {
      sourceType,
      sourceEntity: sourceEntity?.id,
      targetType,
      targetEntity: targetEntity?.id
    });
    
    // Determine association type
    let associationType: AssociationType;
    if (sourceType === 'contact' && targetType === 'company') {
      associationType = 'contact-company';
    } else if (sourceType === 'contact' && targetType === 'contact') {
      associationType = 'contact-contact';
    } else {
      console.log('UnifiedSearch: Unsupported association type', { sourceType, targetType });
      return;
    }
    
    console.log('UnifiedSearch: Setting association data and showing modal', {
      associationType,
      sourceEntity: sourceEntity?.id,
      targetEntity: targetEntity?.id
    });
    
    // Show association modal
    setAssociationData({
      sourceEntity,
      targetEntity,
      associationType,
    });
    setShowAssociationModal(true);
  }, []);

  const closeAssociationModal = useCallback(() => {
    console.log('UnifiedSearch: Closing association modal');
    setShowAssociationModal(false);
    setAssociationData(null);
  }, []);

  // Filter helper tooltip handlers
  const showTooltip = (e: React.MouseEvent, content: string, title?: string) => {
    setTooltip({
      visible: true,
      content,
      title,
      x: e.clientX,
      y: e.clientY
    });
  };

  const hideTooltip = () => {
    setTooltip(prev => ({ ...prev, visible: false }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilters({
      entityType: 'all',
      dealStage: undefined,
      contactRole: undefined,
      companyIndustry: undefined,
      dateRange: undefined,
      linkedStatus: null,
      hasEstimates: false,
      minDealValue: undefined,
      maxDealValue: undefined,
      quickFilter: null
    });
    setSearchTerm('');
  };
  
  // Handle preview actions
  const handlePreviewAction = useCallback((action: string, type: string, id: string) => {
    if (action === 'view') {
      onSelectEntity(type, id);
    } else if (action === 'edit') {
      // Navigate to edit page
      if (type === 'deal') {
        navigate(`/crm/deals/${id}/edit`);
      } else if (type === 'contact') {
        navigate(`/crm/contacts?selected=${id}&action=edit`);
      } else if (type === 'company') {
        navigate(`/crm/companies?selected=${id}&action=edit`);
      }
    }
    hidePreview();
  }, [onSelectEntity, navigate, hidePreview]);

  // Global drag state handlers
  const handleGlobalDragStart = useCallback(() => {
    console.log('UnifiedSearch: Global drag started - disabling hover previews');
    setIsDraggingGlobally(true);
    hidePreview(); // Hide any existing preview
  }, [hidePreview]);

  const handleGlobalDragEnd = useCallback(() => {
    console.log('UnifiedSearch: Global drag ended - enabling hover previews');
    setIsDraggingGlobally(false);
  }, []);


  return (
    <div className="unified-search">
      {/* Command Palette */}
      <CommandPalette 
        isOpen={isCommandPaletteOpen} 
        onClose={() => setIsCommandPaletteOpen(false)} 
      />
      
      {/* Tooltip */}
      <SimpleTooltip tooltip={tooltip} />
      
      {/* Entity Preview */}
      {preview.visible && preview.entity && preview.type && (
        <div 
          className="fixed z-50"
          style={{ 
            left: `${preview.x}px`, 
            top: `${preview.y}px`,
            pointerEvents: 'auto'
          }}
          onMouseEnter={() => setHoveringPreview(true)}
          onMouseLeave={() => setHoveringPreview(false)}
        >
          <EntityPreview
            entity={preview.entity}
            type={preview.type}
            onAction={(action) => handlePreviewAction(action, preview.type!, preview.entity!.id)}
          />
        </div>
      )}
      
      {/* Streamlined Search Header */}
      <div className="mb-6">
        {/* Search Bar with Integrated Controls */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex gap-4 items-center">
            {/* Search Input */}
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => setShowRecentSearches(true)}
                onBlur={() => setTimeout(() => setShowRecentSearches(false), 200)}
                placeholder="Search contacts, companies, deals..."
                className="w-full pl-10 pr-4 py-2.5 bg-gray-50 dark:bg-gray-900 border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
              />
              <svg className="absolute left-3 top-3 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              
              {/* Recent Searches Dropdown */}
              <RecentSearches
                recentSearches={recentSearches}
                onSelectSearch={(query) => {
                  setSearchTerm(query);
                  setShowRecentSearches(false);
                }}
                onClearHistory={clearRecentSearches}
                isVisible={showRecentSearches && !searchTerm}
              />
            </div>
            
            {/* Entity Type Selector */}
            <div className="flex bg-gray-100 dark:bg-gray-900 rounded-lg p-1">
              {(['all', 'contacts', 'companies'/*, 'deals'*/] as EntityType[]).map(type => {
                const count = type === 'all' ? totalResults :
                  type === 'contacts' ? searchResults.contacts.length :
                  type === 'companies' ? searchResults.companies.length :
                  searchResults.deals.length;
                
                return (
                  <button
                    key={type}
                    onClick={() => setFilters(prev => ({ ...prev, entityType: type }))}
                    className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all flex items-center gap-1.5 ${
                      filters.entityType === type
                        ? 'bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400 shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                    }`}
                  >
                    {type === 'contacts' && <UserIcon className="w-3.5 h-3.5" />}
                    {type === 'companies' && <BuildingOffice2Icon className="w-3.5 h-3.5" />}
                    {type === 'deals' && <CurrencyDollarIcon className="w-3.5 h-3.5" />}
                    {type === 'all' ? 'All' : type.charAt(0).toUpperCase() + type.slice(1)}
                    <span className={`ml-1 text-xs ${
                      filters.entityType === type
                        ? 'text-purple-600 dark:text-purple-400'
                        : 'text-gray-500 dark:text-gray-500'
                    }`}>
                      {count}
                    </span>
                  </button>
                );
              })}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`p-2 rounded-lg transition-all flex items-center gap-1.5 ${
                  showFilters 
                    ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
                    : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
                title="Toggle filters (⌘F)"
              >
                <FunnelIcon className="w-4 h-4" />
                {activeFilterCount > 0 && (
                  <span className="text-xs font-medium">{activeFilterCount}</span>
                )}
              </button>
              
              <button
                onClick={exportToCSV}
                className="p-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 rounded-lg transition-all"
                title="Export to CSV"
                disabled={totalResults === 0}
              >
                <ArrowDownTrayIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Quick Filters and Sort - Single Row */}
          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            {/* Quick Filter Presets */}
            <div className="flex gap-2">
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  quickFilter: prev.quickFilter === 'recently-updated' ? null : 'recently-updated' 
                }))}
                className={`px-2.5 py-1 rounded-md text-xs transition-all flex items-center gap-1 ${
                  filters.quickFilter === 'recently-updated'
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <ClockIcon className="w-3 h-3" />
                Recent
              </button>
              
              {/* High Value Deals - HIDDEN
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  quickFilter: prev.quickFilter === 'high-value' ? null : 'high-value',
                  entityType: 'deals'
                }))}
                className={`px-2.5 py-1 rounded-md text-xs transition-all flex items-center gap-1 ${
                  filters.quickFilter === 'high-value'
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <CurrencyDollarIcon className="w-3 h-3" />
                High Value
              </button> */}
              
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  quickFilter: prev.quickFilter === 'unlinked' ? null : 'unlinked',
                  entityType: 'companies'
                }))}
                className={`px-2.5 py-1 rounded-md text-xs transition-all flex items-center gap-1 ${
                  filters.quickFilter === 'unlinked'
                    ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <LinkIcon className="w-3 h-3" />
                Unlinked
              </button>
              
              <button
                onClick={() => setFilters(prev => ({ 
                  ...prev, 
                  quickFilter: prev.quickFilter === 'my-items' ? null : 'my-items' 
                }))}
                className={`px-2.5 py-1 rounded-md text-xs transition-all flex items-center gap-1 ${
                  filters.quickFilter === 'my-items'
                    ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
                }`}
              >
                <UserIcon className="w-3 h-3" />
                Mine
              </button>
            </div>
            
            {/* Sort Selector */}
            {totalResults > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500 dark:text-gray-400">Sort:</span>
                <select
                  value={sortConfig ? `${sortConfig.field}-${sortConfig.order}` : ''}
                  onChange={(e) => {
                    if (e.target.value) {
                      const [field, order] = e.target.value.split('-') as [SortField, 'asc' | 'desc'];
                      setSortConfig({ field, order });
                    }
                  }}
                  className="px-2 py-1 text-xs bg-gray-50 dark:bg-gray-900 border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
                >
                  <option value="">Default</option>
                  <option value="name-asc">Name A-Z</option>
                  <option value="name-desc">Name Z-A</option>
                  <option value="created-desc">Newest First</option>
                  <option value="created-asc">Oldest First</option>
                  <option value="updated-desc">Recently Updated</option>
                  <option value="value-desc">Highest Value</option>
                  <option value="value-asc">Lowest Value</option>
                </select>
              </div>
            )}
          </div>
        </div>
        
        {/* Active Filters Summary - Minimal */}
        {activeFilterCount > 0 && (
          <div className="mt-3 flex items-center gap-2">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {totalResults} results with {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''}
            </span>
            <div className="flex-1 flex flex-wrap gap-1">
              {filters.dealStage?.map(stage => (
                <span key={stage} className="inline-flex items-center gap-1 px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-xs text-gray-600 dark:text-gray-400 rounded-md">
                  {stage}
                  <button
                    onClick={() => setFilters(prev => ({
                      ...prev,
                      dealStage: prev.dealStage?.filter(s => s !== stage)
                    }))}
                    className="hover:text-red-600"
                  >
                    <XMarkIcon className="w-2.5 h-2.5" />
                  </button>
                </span>
              ))}
              {filters.linkedStatus && (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-xs text-gray-600 dark:text-gray-400 rounded-md">
                  {filters.linkedStatus.replace('_', ' ')}
                  <button
                    onClick={() => setFilters(prev => ({ ...prev, linkedStatus: null }))}
                    className="hover:text-red-600"
                  >
                    <XMarkIcon className="w-2.5 h-2.5" />
                  </button>
                </span>
              )}
            </div>
            <button
              onClick={clearAllFilters}
              className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
            >
              Clear all
            </button>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      <div className="flex gap-4">
        {/* Filter Panel */}
        {showFilters && (
          <div className="w-80 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 h-fit">
            <div className="flex items-center justify-between mb-6">
              <h3 className="font-semibold text-gray-900 dark:text-white">Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
            
            {/* Filter Sections */}
            <div className="space-y-6">
              {/* Deal Filters - HIDDEN */}
              {/* {(filters.entityType === 'all' || filters.entityType === 'deals') && filterOptions.dealStages.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Deal Stage</label>
                    <button
                      onMouseEnter={(e) => showTooltip(e, 'Filter deals by their current stage in the pipeline')}
                      onMouseLeave={hideTooltip}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <QuestionMarkCircleIcon className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="space-y-2">
                    {filterOptions.dealStages.map(stage => (
                      <label key={stage} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.dealStage?.includes(stage) || false}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFilters(prev => ({
                                ...prev,
                                dealStage: [...(prev.dealStage || []), stage]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                dealStage: prev.dealStage?.filter(s => s !== stage)
                              }));
                            }
                          }}
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{stage}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )} */}
              
              {/* Company Industry Filter */}
              {(filters.entityType === 'all' || filters.entityType === 'companies') && filterOptions.industries.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Industry</label>
                    <button
                      onMouseEnter={(e) => showTooltip(e, 'Filter companies by their industry sector')}
                      onMouseLeave={hideTooltip}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <QuestionMarkCircleIcon className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {filterOptions.industries.map(industry => (
                      <label key={industry} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.companyIndustry?.includes(industry) || false}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFilters(prev => ({
                                ...prev,
                                companyIndustry: [...(prev.companyIndustry || []), industry]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                companyIndustry: prev.companyIndustry?.filter(i => i !== industry)
                              }));
                            }
                          }}
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{industry}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Linked Status Filter */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Integration Status</label>
                  <button
                    onMouseEnter={(e) => showTooltip(e, 'Filter by external system integration status')}
                    onMouseLeave={hideTooltip}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <QuestionMarkCircleIcon className="w-4 h-4" />
                  </button>
                </div>
                <select
                  value={filters.linkedStatus || ''}
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    linkedStatus: e.target.value as FilterState['linkedStatus'] || null 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white text-sm"
                >
                  <option value="">All</option>
                  <option value="both">Linked to Both</option>
                  <option value="hubspot_only">HubSpot Only</option>
                  <option value="harvest_only">Harvest Only</option>
                  <option value="none">Not Linked</option>
                </select>
              </div>
              
              {/* Deal Value Range - HIDDEN */}
              {/* {(filters.entityType === 'all' || filters.entityType === 'deals') && (
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 block mb-3">
                    Deal Value Range
                  </label>
                  <div className="flex gap-2 items-center">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filters.minDealValue || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        minDealValue: e.target.value ? Number(e.target.value) : undefined 
                      }))}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                    <span className="text-gray-500">to</span>
                    <input
                      type="number"
                      placeholder="Max"
                      value={filters.maxDealValue || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        maxDealValue: e.target.value ? Number(e.target.value) : undefined 
                      }))}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white text-sm"
                    />
                  </div>
                </div>
              )} */}
            </div>
          </div>
        )}
        
        {/* Results Grid */}
        <div className={`flex-1 grid grid-cols-1 ${showFilters ? 'lg:grid-cols-2' : 'lg:grid-cols-2'} gap-6`}>
          {/* Contacts Column */}
          {(filters.entityType === 'all' || filters.entityType === 'contacts') && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide flex items-center gap-2">
                  <UserIcon className="w-4 h-4" />
                  Contacts ({searchResults.contacts.length})
                </h3>
                <button
                  onClick={() => navigate('/crm/contacts')}
                  className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 hover:underline"
                >
                  View all contacts →
                </button>
              </div>
              <div className="space-y-2">
                {searchResults.contacts.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                    No contacts found
                  </div>
                ) : (
                  searchResults.contacts.slice(0, visibleCounts.contacts).map(contact => (
                    <DraggableEntity
                      key={contact.id}
                      entity={contact}
                      entityType="contact"
                      onDrop={handleDrop}
                      onDragStart={handleGlobalDragStart}
                      onDragEnd={handleGlobalDragEnd}
                    >
                      <div
                        onClick={() => onSelectEntity('contact', contact.id)}
                        onMouseEnter={(e) => !isDraggingGlobally && showPreview(e, contact, 'contact')}
                        onMouseLeave={hidePreview}
                        className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-500 cursor-pointer transition-all hover:shadow-md group"
                      >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold flex-shrink-0">
                          {contact?.firstName?.charAt(0) || contact?.lastName?.charAt(0) || '?'}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 dark:text-white truncate group-hover:text-purple-600 dark:group-hover:text-purple-400">
                            {highlightText(`${contact?.firstName || ''} ${contact?.lastName || ''}`.trim() || 'Unnamed Contact', searchTerm)}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {highlightText(contact?.email || 'No email', searchTerm)}
                          </div>
                          {contact?.roles?.length > 0 && (
                            <div className="flex gap-1 mt-1">
                              {contact.roles.slice(0, 2).map(role => (
                                <Badge key={role} variant="neutral" size="sm">
                                  {role}
                                </Badge>
                              ))}
                              {contact.roles.length > 2 && (
                                <Badge variant="neutral" size="sm">+{contact.roles.length - 2}</Badge>
                              )}
                            </div>
                          )}
                        </div>
                        {/* Linking indicators */}
                        {(contact?.companies?.length > 0 || contact?.relationships?.length > 0) && (
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {contact?.companies?.length > 0 && (
                              <div className="flex items-center gap-0.5 text-gray-400" title={`Linked to ${contact.companies.length} compan${contact.companies.length === 1 ? 'y' : 'ies'}`}>
                                <BuildingOffice2Icon className="w-3.5 h-3.5" />
                                <span className="text-xs">{contact.companies.length}</span>
                              </div>
                            )}
                            {contact?.relationships?.length > 0 && (
                              <div className="flex items-center gap-0.5 text-gray-400" title={`${contact.relationships.length} relationship${contact.relationships.length === 1 ? '' : 's'}`}>
                                <LinkIcon className="w-3.5 h-3.5" />
                                <span className="text-xs">{contact.relationships.length}</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    </DraggableEntity>
                  ))
                )}
                {/* Show more button - REMOVED - Loading all records
                {searchResults.contacts.length > visibleCounts.contacts && (
                  <div className="text-center py-2">
                    <button 
                      onClick={() => setVisibleCounts(prev => ({ ...prev, contacts: prev.contacts + 10 }))}
                      className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                    >
                      Show {Math.min(10, searchResults.contacts.length - visibleCounts.contacts)} more
                    </button>
                  </div>
                )} */}
              </div>
            </div>
          )}

          {/* Companies Column */}
          {(filters.entityType === 'all' || filters.entityType === 'companies') && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide flex items-center gap-2">
                  <BuildingOffice2Icon className="w-4 h-4" />
                  Companies ({searchResults.companies.length})
                </h3>
                <button
                  onClick={() => navigate('/crm/companies')}
                  className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 hover:underline"
                >
                  View all companies →
                </button>
              </div>
              <div className="space-y-2">
                {searchResults.companies.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                    No companies found
                  </div>
                ) : (
                  searchResults.companies.slice(0, visibleCounts.companies).map(company => (
                    <DraggableEntity
                      key={company.id}
                      entity={company}
                      entityType="company"
                      onDrop={handleDrop}
                      onDragStart={handleGlobalDragStart}
                      onDragEnd={handleGlobalDragEnd}
                    >
                      <div
                        onClick={() => onSelectEntity('company', company.id)}
                        onMouseEnter={(e) => !isDraggingGlobally && showPreview(e, company, 'company')}
                        onMouseLeave={hidePreview}
                        className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-500 cursor-pointer transition-all hover:shadow-md group"
                      >
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                          <BuildingOffice2Icon className="w-5 h-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 dark:text-white truncate group-hover:text-purple-600 dark:group-hover:text-purple-400">
                            {highlightText(company?.name || 'Unnamed Company', searchTerm)}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate flex items-center gap-2">
                            {company?.industry && (
                              <>
                                <BuildingStorefrontIcon className="w-3 h-3" />
                                {highlightText(company.industry, searchTerm)}
                              </>
                            )}
                          </div>
                          {company?.linkedStatus && company.linkedStatus !== 'none' && (
                            <div className="flex gap-1 mt-1">
                              {(company.linkedStatus === 'both' || company.linkedStatus === 'hubspot_only') && (
                                <Badge variant="secondary" size="sm">
                                  HubSpot
                                </Badge>
                              )}
                              {(company.linkedStatus === 'both' || company.linkedStatus === 'harvest_only') && (
                                <Badge variant="secondary" size="sm">
                                  Harvest
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                        {/* Linking indicators and enrichment status */}
                        <div className="flex items-center gap-2 flex-shrink-0">
                          {company?.contactCount > 0 && (
                            <div className="flex items-center gap-0.5 text-gray-400" title={`${company.contactCount} contact${company.contactCount === 1 ? '' : 's'}`}>
                              <UserIcon className="w-3.5 h-3.5" />
                              <span className="text-xs">{company.contactCount}</span>
                            </div>
                          )}
                          {company?.activeDealsCount > 0 && (
                            <div className="flex items-center gap-0.5 text-gray-400" title={`${company.activeDealsCount} active deal${company.activeDealsCount === 1 ? '' : 's'}`}>
                              <CurrencyDollarIcon className="w-3.5 h-3.5" />
                              <span className="text-xs">{company.activeDealsCount}</span>
                            </div>
                          )}
                          {/* Enrichment status indicator */}
                          {company?.enrichmentStatus?.sources?.abn_lookup?.success && (
                            <Badge variant="success" size="sm" title="ABN Verified">ABN</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    </DraggableEntity>
                  ))
                )}
                {/* Show more button - REMOVED - Loading all records
                {searchResults.companies.length > visibleCounts.companies && (
                  <div className="text-center py-2">
                    <button 
                      onClick={() => setVisibleCounts(prev => ({ ...prev, companies: prev.companies + 10 }))}
                      className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                    >
                      Show {Math.min(10, searchResults.companies.length - visibleCounts.companies)} more
                    </button>
                  </div>
                )} */}
              </div>
            </div>
          )}

          {/* Deals Column - HIDDEN */}
          {/* {(filters.entityType === 'all' || filters.entityType === 'deals') && (
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide flex items-center gap-2">
                  <CurrencyDollarIcon className="w-4 h-4" />
                  Deals ({searchResults.deals.length})
                </h3>
                <button
                  onClick={() => navigate('/crm/deals')}
                  className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 hover:underline"
                >
                  View all deals →
                </button>
              </div>
              <div className="space-y-2">
                {searchResults.deals.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400 text-sm">
                    No deals found
                  </div>
                ) : (
                  searchResults.deals.slice(0, visibleCounts.deals).map(deal => (
                    <div
                      key={deal.id}
                      onClick={() => onSelectEntity('deal', deal.id)}
                      onMouseEnter={(e) => showPreview(e, deal, 'deal')}
                      onMouseLeave={hidePreview}
                      className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-500 cursor-pointer transition-all hover:shadow-md group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 min-w-0 flex-1">
                          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center text-white flex-shrink-0">
                            <CurrencyDollarIcon className="w-5 h-5" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-gray-900 dark:text-white truncate group-hover:text-purple-600 dark:group-hover:text-purple-400">
                              {highlightText(deal?.name || 'Untitled Deal', searchTerm)}
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Badge variant={deal?.stage?.toLowerCase().includes('closed') ? 'success' : 'secondary'} size="sm">
                                {deal?.stage || 'No stage'}
                              </Badge>
                              {deal?.hasEstimates && (
                                <Badge variant="predicted" size="sm">
                                  Has estimates
                                </Badge>
                              )}
                            </div>
                            {deal?.company && (
                              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {highlightText(
                                  typeof deal.company === 'string' ? deal.company : deal.company.name || '',
                                  searchTerm
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-right ml-3 flex-shrink-0">
                          <div className="font-semibold text-gray-900 dark:text-white">
                            ${(deal?.value || deal?.amount || 0).toLocaleString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {deal?.probability || 0}% likely
                          </div>
                          {deal?.closeDate && (
                            <div className="text-xs text-gray-500 mt-1">
                              <CalendarIcon className="w-3 h-3 inline mr-1" />
                              {new Date(deal.closeDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
                {searchResults.deals.length > visibleCounts.deals && (
                  <div className="text-center py-2">
                    <button 
                      onClick={() => setVisibleCounts(prev => ({ ...prev, deals: prev.deals + 10 }))}
                      className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                    >
                      Show {Math.min(10, searchResults.deals.length - visibleCounts.deals)} more
                    </button>
                  </div>
                )}
              </div>
            </div>
          )} */}
        </div>
      </div>

      {/* Empty State */}
      {totalResults === 0 && (searchTerm || activeFilterCount > 0) && (
        <div className="text-center py-12">
          <svg className="w-16 h-16 mx-auto text-gray-300 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
            No results found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchTerm && activeFilterCount > 0
              ? `No results for "${searchTerm}" with current filters`
              : searchTerm
              ? `No results for "${searchTerm}"`
              : 'No results match your filters'
            }
          </p>
          {activeFilterCount > 0 && (
            <button
              onClick={clearAllFilters}
              className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
            >
              Clear all filters
            </button>
          )}
        </div>
      )}
      
      {/* Keyboard shortcuts hint */}
      <div className="mt-8 text-center text-xs text-gray-500 dark:text-gray-400">
        Press <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">⌘K</kbd> for quick search, 
        <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded ml-1">⌘F</kbd> to toggle filters
      </div>

      {/* Association Modal */}
      {showAssociationModal && associationData && (
        <AssociationModal
          isOpen={showAssociationModal}
          onClose={closeAssociationModal}
          sourceEntity={associationData.sourceEntity}
          targetEntity={associationData.targetEntity}
          associationType={associationData.associationType}
        />
      )}
    </div>
  );
};

export default UnifiedSearch;