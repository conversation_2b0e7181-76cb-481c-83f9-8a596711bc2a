/**
 * Radar Actions API Client
 * 
 * Frontend API client for managing radar action items
 */

import type {
  RadarAction,
  CreateRadarAction,
  UpdateRadarAction,
  RadarActionFilters,
  RadarActionStatus
} from '../../types/radar-action-types';
import { API_BASE_URL } from './utils';

const BASE_URL = `${API_BASE_URL}/radar-actions`;

/**
 * Get all radar actions with optional filters
 */
export async function getRadarActions(filters?: RadarActionFilters): Promise<RadarAction[]> {
  const queryParams = new URLSearchParams();
  
  if (filters) {
    if (filters.companyId) queryParams.append('companyId', filters.companyId);
    if (filters.assignedTo) queryParams.append('assignedTo', filters.assignedTo);
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        filters.status.forEach(s => queryParams.append('status', s));
      } else {
        queryParams.append('status', filters.status);
      }
    }
    if (filters.priority) {
      if (Array.isArray(filters.priority)) {
        filters.priority.forEach(p => queryParams.append('priority', p));
      } else {
        queryParams.append('priority', filters.priority);
      }
    }
    if (filters.actionType) {
      if (Array.isArray(filters.actionType)) {
        filters.actionType.forEach(t => queryParams.append('actionType', t));
      } else {
        queryParams.append('actionType', filters.actionType);
      }
    }
    if (filters.dueBefore) queryParams.append('dueBefore', filters.dueBefore);
    if (filters.dueAfter) queryParams.append('dueAfter', filters.dueAfter);
    if (filters.includeCompleted !== undefined) {
      queryParams.append('includeCompleted', filters.includeCompleted.toString());
    }
  }

  const response = await fetch(`${BASE_URL}?${queryParams}`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch radar actions: ${response.statusText}`);
  }

  const data = await response.json();
  return data.actions;
}

/**
 * Get actions for a specific company
 */
export async function getCompanyRadarActions(
  companyId: string,
  includeCompleted: boolean = false
): Promise<RadarAction[]> {
  const response = await fetch(
    `${BASE_URL}/company/${companyId}?includeCompleted=${includeCompleted}`,
    {
      credentials: 'include',
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch company radar actions: ${response.statusText}`);
  }

  const data = await response.json();
  return data.actions;
}

/**
 * Get actions assigned to a specific user
 */
export async function getUserRadarActions(
  assignedTo: string,
  includeCompleted: boolean = false
): Promise<RadarAction[]> {
  const response = await fetch(
    `${BASE_URL}/user/${assignedTo}?includeCompleted=${includeCompleted}`,
    {
      credentials: 'include',
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch user radar actions: ${response.statusText}`);
  }

  const data = await response.json();
  return data.actions;
}

/**
 * Get overdue actions
 */
export async function getOverdueRadarActions(): Promise<RadarAction[]> {
  const response = await fetch(`${BASE_URL}/overdue`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch overdue radar actions: ${response.statusText}`);
  }

  const data = await response.json();
  return data.actions;
}

/**
 * Get action statistics
 */
export async function getRadarActionStats(): Promise<Record<RadarActionStatus, number>> {
  const response = await fetch(`${BASE_URL}/stats`, {
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch radar action stats: ${response.statusText}`);
  }

  const data = await response.json();
  return data.statusCounts;
}

/**
 * Get a single radar action by ID
 */
export async function getRadarAction(id: string, includeCompany: boolean = true): Promise<RadarAction> {
  const response = await fetch(
    `${BASE_URL}/${id}?includeCompany=${includeCompany}`,
    {
      credentials: 'include',
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch radar action: ${response.statusText}`);
  }

  const data = await response.json();
  return data.action;
}

/**
 * Create a new radar action
 */
export async function createRadarAction(action: Omit<CreateRadarAction, 'createdBy'>): Promise<RadarAction> {
  const response = await fetch(BASE_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(action),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create radar action');
  }

  const data = await response.json();
  return data.action;
}

/**
 * Update a radar action
 */
export async function updateRadarAction(
  id: string,
  updates: Omit<UpdateRadarAction, 'updatedBy'>
): Promise<RadarAction> {
  const response = await fetch(`${BASE_URL}/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(updates),
  });

  if (!response.ok) {
    throw new Error(`Failed to update radar action: ${response.statusText}`);
  }

  const data = await response.json();
  return data.action;
}

/**
 * Complete a radar action
 */
export async function completeRadarAction(
  id: string,
  completionNotes?: string
): Promise<RadarAction> {
  const response = await fetch(`${BASE_URL}/${id}/complete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ completionNotes }),
  });

  if (!response.ok) {
    throw new Error(`Failed to complete radar action: ${response.statusText}`);
  }

  const data = await response.json();
  return data.action;
}

/**
 * Cancel a radar action
 */
export async function cancelRadarAction(
  id: string,
  notes?: string
): Promise<RadarAction> {
  const response = await fetch(`${BASE_URL}/${id}/cancel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ notes }),
  });

  if (!response.ok) {
    throw new Error(`Failed to cancel radar action: ${response.statusText}`);
  }

  const data = await response.json();
  return data.action;
}

/**
 * Delete a radar action
 */
export async function deleteRadarAction(id: string): Promise<void> {
  const response = await fetch(`${BASE_URL}/${id}`, {
    method: 'DELETE',
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to delete radar action: ${response.statusText}`);
  }
}