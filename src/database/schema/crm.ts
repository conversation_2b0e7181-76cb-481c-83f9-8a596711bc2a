/**
 * CRM domain schema
 *
 * This file contains the schema for the CRM domain tables.
 */

import BetterSqlite3 from 'better-sqlite3';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Create the CRM domain tables
 * @param db Database instance
 */
export function createCRMTables(db: Database): void {
  console.log('Creating CRM domain tables...');

  // Create deal table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS deal (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      stage TEXT NOT NULL,
      status TEXT, -- Separate from stage for clearer state management

      /* Financial Information */
      value REAL,
      currency TEXT DEFAULT 'AUD',
      probability REAL,

      /* Timing Information */
      expected_close_date TEXT,
      start_date TEXT,
      end_date TEXT,

      /* Invoicing Information */
      invoice_frequency TEXT,
      payment_terms INTEGER,

      /* Relationships */
      company_id TEXT NOT NULL,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_project_id TEXT,

      /* Additional Information */
      description TEXT,
      source TEXT, -- 'HubSpot', 'Manual'
      priority TEXT,
      owner TEXT,
      custom_fields TEXT, -- JSON string for custom fields
      include_in_projections INTEGER DEFAULT 1,
      deleted_at TEXT, -- Soft delete implementation
      
      /* Direct Estimate Reference (added via migration 003) */
      estimate_id TEXT REFERENCES estimate(id) ON DELETE SET NULL,
      estimate_type TEXT CHECK(estimate_type IN ('internal', 'harvest', NULL)),

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT NOT NULL,
      updated_by TEXT NOT NULL,

      FOREIGN KEY (company_id) REFERENCES company(id)
    )
  `).run();

  // Create indexes for deal table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_stage ON deal(stage)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_status ON deal(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_expected_close_date ON deal(expected_close_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_company_id ON deal(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_hubspot_id ON deal(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_harvest_project_id ON deal(harvest_project_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_source ON deal(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_deleted_at ON deal(deleted_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_include_in_projections ON deal(include_in_projections)`).run();

  // Create contact table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact (
      id TEXT PRIMARY KEY,
      first_name TEXT NOT NULL,
      last_name TEXT,
      email TEXT,
      phone TEXT,
      job_title TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_user_id TEXT UNIQUE,

      /* Source and Status Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'
      deleted_at TEXT, -- Soft delete implementation

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      notes TEXT,
      
      /* Enrichment Fields (added via migration 005) */
      enrichment_status JSON,
      last_enriched_at TIMESTAMP
    )
  `).run();

  // Create indexes for contact table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_name ON contact(first_name, last_name)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_email ON contact(email)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_hubspot_id ON contact(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_harvest_user_id ON contact(harvest_user_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_source ON contact(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_deleted_at ON contact(deleted_at)`).run();

  // Create company table with unified fields for Radar companies
  db.prepare(`
    CREATE TABLE IF NOT EXISTS company (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      industry TEXT,
      size TEXT,
      website TEXT,
      address TEXT,
      description TEXT,

      /* External System IDs */
      hubspot_id TEXT UNIQUE,
      harvest_id TEXT UNIQUE,

      /* Source Information */
      source TEXT, -- 'HubSpot', 'Harvest', 'Manual'

      /* Radar-specific Fields */
      radar_state TEXT, -- 'Strategy', 'Transformation', 'BAU', 'Transition out'
      priority TEXT, -- 'High', 'Medium', 'Low', 'Qualified out'
      current_spend REAL,
      potential_spend REAL,
      last_interaction_date TEXT,

      /* Audit Information */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT, -- For soft deletes
      
      /* Additional Fields (added via migration) */
      notes TEXT, -- Company-specific notes
      contacts INTEGER, -- Number of contacts (for Radar view)
      
      /* Enrichment Fields (added via migration 005) */
      enrichment_status JSON,
      last_enriched_at TIMESTAMP
    )
  `).run();

  // Create indexes for company table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_name ON company(name)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_hubspot_id ON company(hubspot_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_harvest_id ON company(harvest_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_source ON company(source)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_radar_state ON company(radar_state)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_priority ON company(priority)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_deleted_at ON company(deleted_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_company_last_interaction_date ON company(last_interaction_date)`).run();

  // Create deal_estimate junction table (one-to-one relationship as of migration 003)
  db.prepare(`
    CREATE TABLE IF NOT EXISTS deal_estimate (
      id TEXT PRIMARY KEY,
      deal_id TEXT NOT NULL UNIQUE, -- UNIQUE constraint ensures one estimate per deal
      estimate_id TEXT NOT NULL UNIQUE, -- UNIQUE constraint ensures estimate belongs to only one deal
      estimate_type TEXT NOT NULL CHECK(estimate_type IN ('internal', 'harvest')),
      harvest_estimate_id TEXT,
      linked_at TEXT DEFAULT (datetime('now')),
      linked_by TEXT DEFAULT 'system',
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for deal_estimate table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_estimate_deal_id ON deal_estimate(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_estimate_estimate_id ON deal_estimate(estimate_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_deal_estimate_type ON deal_estimate(estimate_type)`).run();

  // Create contact_company junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_company (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contact_id TEXT NOT NULL,
      company_id TEXT NOT NULL,
      role TEXT,
      is_primary BOOLEAN DEFAULT FALSE,
      created_at TEXT DEFAULT (datetime('now')),
      created_by TEXT,
      updated_at TEXT DEFAULT (datetime('now')),
      updated_by TEXT,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
      UNIQUE(contact_id, company_id)
    )
  `).run();

  // Create indexes for contact_company table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_contact ON contact_company(contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_company ON contact_company(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_role ON contact_company(role)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_company_is_primary ON contact_company(is_primary)`).run();

  // Create note table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS note (
      id TEXT PRIMARY KEY,
      content TEXT NOT NULL,
      entity_type TEXT NOT NULL,
      entity_id TEXT NOT NULL,
      author TEXT,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT,
      
      /* Conversation Threading Fields (added via migration 002) */
      parent_note_id TEXT REFERENCES note(id) ON DELETE SET NULL,
      thread_id TEXT, -- Groups related conversations
      participants TEXT, -- JSON array of contact IDs
      conversation_type TEXT CHECK(conversation_type IN ('email', 'call', 'meeting', 'slack', 'internal', NULL)),
      status TEXT DEFAULT 'open' CHECK(status IN ('open', 'resolved', 'parked', 'archived'))
    )
  `).run();

  // Create indexes for note table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_entity ON note(entity_type, entity_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_created_at ON note(created_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_thread ON note(thread_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_parent ON note(parent_note_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_status ON note(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_note_conversation_type ON note(conversation_type)`).run();

  // Create contact_role junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_role (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contact_id TEXT NOT NULL,
      deal_id TEXT NOT NULL,
      role TEXT,
      created_at TEXT DEFAULT (datetime('now')),
      created_by TEXT,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
      UNIQUE(contact_id, deal_id)
    )
  `).run();

  // Create indexes for contact_role table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_role_deal_id ON contact_role(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_role_contact_id ON contact_role(contact_id)`).run();

  // Create project table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS project (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'active',
      project_type TEXT,
      
      /* Timing */
      start_date TEXT,
      end_date TEXT,
      
      /* Financial */
      budget REAL,
      spent REAL,
      currency TEXT DEFAULT 'AUD',
      
      /* External IDs */
      harvest_project_id TEXT UNIQUE,
      
      /* Relationships */
      company_id TEXT NOT NULL,
      deal_id TEXT,
      
      /* Metadata */
      tags TEXT,
      custom_fields TEXT,
      
      /* Audit */
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL,
      created_by TEXT,
      updated_by TEXT,
      deleted_at TEXT,
      
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
    )
  `).run();

  // Create indexes for project table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_status ON project(status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_company_id ON project(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_deal_id ON project(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_harvest_project_id ON project(harvest_project_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_deleted_at ON project(deleted_at)`).run();

  // Create project_contact junction table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS project_contact (
      project_id TEXT NOT NULL,
      contact_id TEXT NOT NULL,
      role TEXT NOT NULL,
      allocation_percentage REAL,
      start_date TEXT,
      end_date TEXT,
      
      /* Audit */
      created_at TEXT NOT NULL,
      created_by TEXT,
      updated_at TEXT,
      updated_by TEXT,
      
      PRIMARY KEY (project_id, contact_id),
      FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE CASCADE,
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
    )
  `).run();

  // Create indexes for project_contact table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_contact_project_id ON project_contact(project_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_contact_contact_id ON project_contact(contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_contact_role ON project_contact(role)`).run();

  // Create project_dependency table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS project_dependency (
      id TEXT PRIMARY KEY,
      predecessor_project_id TEXT NOT NULL,
      successor_project_id TEXT NOT NULL,
      dependency_type TEXT NOT NULL,
      lag_days INTEGER DEFAULT 0,
      
      /* Audit */
      created_at TEXT NOT NULL,
      created_by TEXT,
      
      FOREIGN KEY (predecessor_project_id) REFERENCES project(id) ON DELETE CASCADE,
      FOREIGN KEY (successor_project_id) REFERENCES project(id) ON DELETE CASCADE,
      UNIQUE(predecessor_project_id, successor_project_id)
    )
  `).run();

  // Create indexes for project_dependency table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_dependency_predecessor ON project_dependency(predecessor_project_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_project_dependency_successor ON project_dependency(successor_project_id)`).run();

  // Create contact_relationships table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS contact_relationships (
      id TEXT PRIMARY KEY,
      source_contact_id TEXT NOT NULL,
      target_contact_id TEXT NOT NULL,
      relationship_type TEXT NOT NULL, -- 'knows', 'reports_to', 'introduced_by', 'worked_with', 'colleague'
      strength INTEGER DEFAULT 1 CHECK(strength >= 1 AND strength <= 5), -- 1-5 scale
      context TEXT, -- Free text description of the relationship
      created_at TEXT NOT NULL DEFAULT (datetime('now')),
      created_by TEXT,
      updated_at TEXT DEFAULT (datetime('now')),
      updated_by TEXT,
      FOREIGN KEY (source_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      FOREIGN KEY (target_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      -- Ensure no duplicate relationships
      UNIQUE(source_contact_id, target_contact_id, relationship_type)
    )
  `).run();

  // Create indexes for contact_relationships table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_relationships_source ON contact_relationships(source_contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_relationships_target ON contact_relationships(target_contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_contact_relationships_type ON contact_relationships(relationship_type)`).run();

  // Create team_contact_coverage table
  db.prepare(`
    CREATE TABLE IF NOT EXISTS team_contact_coverage (
      id TEXT PRIMARY KEY,
      contact_id TEXT NOT NULL,
      team_member_id TEXT NOT NULL, -- References Harvest user ID
      relationship_strength TEXT CHECK(relationship_strength IN ('primary', 'secondary', 'minimal')),
      last_interaction_date TEXT,
      last_interaction_type TEXT,
      notes TEXT,
      created_at TEXT NOT NULL DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
      UNIQUE(contact_id, team_member_id)
    )
  `).run();

  // Create indexes for team_contact_coverage table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_team_coverage_contact ON team_contact_coverage(contact_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_team_coverage_member ON team_contact_coverage(team_member_id)`).run();


  // Create tender table for qualification workflow
  db.prepare(`
    CREATE TABLE IF NOT EXISTS tender (
      id TEXT PRIMARY KEY,
      
      /* Tender Information (from email) */
      request_no TEXT UNIQUE NOT NULL,
      status TEXT DEFAULT 'Current', -- 'Current', 'Closed', etc.
      type TEXT DEFAULT 'Tender',
      summary TEXT NOT NULL,
      issued_by TEXT NOT NULL,
      unspsc TEXT, -- Universal Standard Products and Services Code
      closing_date TEXT NOT NULL,
      
      /* Email Source */
      source_email TEXT, -- Original email content
      tender_url TEXT, -- Link to tender details
      additional_info TEXT, -- Additional information from web scraping
      
      /* Qualification Workflow */
      qualification_status TEXT DEFAULT 'new' CHECK(qualification_status IN ('new', 'reviewing', 'not_interested', 'interested')),
      qualification_reason TEXT, -- Why qualified in/out
      qualified_by TEXT,
      qualified_at TEXT,
      
      /* Relationships */
      company_id TEXT, -- Auto-linked based on issued_by
      deal_id TEXT, -- Created when moved to 'interested'
      
      /* Metadata */
      tags TEXT, -- JSON array of tags
      notes TEXT,
      
      /* Audit */
      created_at TEXT NOT NULL DEFAULT (datetime('now')),
      updated_at TEXT NOT NULL DEFAULT (datetime('now')),
      created_by TEXT DEFAULT 'email_ingestion',
      updated_by TEXT,
      deleted_at TEXT, -- Soft delete
      
      FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
      FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
    )
  `).run();

  // Create indexes for tender table
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_request_no ON tender(request_no)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_qualification_status ON tender(qualification_status)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_closing_date ON tender(closing_date)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_company_id ON tender(company_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_deal_id ON tender(deal_id)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_issued_by ON tender(issued_by)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_created_at ON tender(created_at)`).run();
  db.prepare(`CREATE INDEX IF NOT EXISTS idx_tender_deleted_at ON tender(deleted_at)`).run();

  console.log('CRM domain tables created successfully');
}
